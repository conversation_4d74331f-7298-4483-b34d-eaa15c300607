# -*- coding: utf-8 -*-
import json
import os

import pydash

from src.apps.base_curd import BaseCURD
from src.apps.model.req_schema import ModelSquareReq, ModelCreateOrUpdate
from src.apps.model.rsp_schema import Model, ModelBase, ModelInfEngine, ModelDetail
from src.apps.tag.curd import tag_curd
from src.apps.tag.rsp_schema import ModelTag
from src.common.const.comm_const import ResourceModule, DataOper, ModelStatus, SourceChannel, JobAction, JobStatus, \
    PUBLIC_MODEL_DIR
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.exceptions import MaaSBaseException
from src.common.k8s_resource.base_operation import create_download_model_job
from src.common.utils.data import transform_to_model
from src.common.utils.id_tool import uuid
from src.setting import settings
from src.system.db.sync_db import session_manage

UPDATE_FIELDS = ['name', 'icon', 'brief', 'tags', 'n_params', 'requirements', 'port', 'environment', 'inference_engine', 'mounts', 'image', 'deploy_editable']
EXT_FIELDS = ['source_model']


class ModelCURD(BaseCURD[Model]):

    def __init__(self):
        super(ModelCURD, self).__init__()

    async def _valid_name(self, name: str, provider: str):
        expr = [
            {"column_name": "name", "value": name},
            {"column_name": "provider", "value": provider},
            {"column_name": "status", "operator": "ne", "value": ModelStatus.DELETED}
        ]
        if await self.get_by_filter(expr):
            raise MaaSBaseException(Err.OBJECT_EXISTS, fields=name)

    @session_manage()
    async def list_square(self, req: ModelSquareReq) -> (list[ModelBase], int):
        sql = f"select * from model where status = '{ModelStatus.APPROVED.value}' and model.status = '{ModelStatus.APPROVED.value}'"
        params = {}
        if req.keyword:
            sql += f" and (name like :keyword or developer like :keyword)"
            params["keyword"] = f'%{req.keyword}%'
        if req.tags:
            sql += f" and tags && ARRAY[:tags]::varchar[]"
            params["tags"] = req.tags
        sql += " order by seq desc, create_time desc"
        rows, total = await self.query_by_sql(sql, params, True, req)
        return [ModelBase(**row) for row in rows], total

    def save(self, model: ModelCreateOrUpdate):
        """
        保存模型（新增或者修改公共逻辑）
        :param model:
        :return:
        """
        # 更新模型推理引擎数据
        self.session.query(ModelInfEngine).filter_by(model_id=model.id).delete()
        if model.inference_engine:
            for engine in model.inference_engine:
                data = engine.model_dump()
                data.update({"model_id": model.id})
                self.session.add(transform_to_model(ModelInfEngine, data, DataOper.CREATE))

    @session_manage()
    async def create(self, model: ModelCreateOrUpdate) -> str:
        user = Context.USER.get()
        await self._valid_name(model.name, user.user_id)
        model.id = uuid(prefix=ResourceModule.MODEL, exclude_uppercase=True)
        tag_curd.save_resource_tag(model.id, model.tags)
        model_dict = model.model_dump()
        model_dict.update({
            'status': ModelStatus.DRAFT.value,
            'developer': user.user_id,
            'provider': user.user_id,
            'creator': user.user_id,
            'ext': {}
        })
        for field in EXT_FIELDS:
            if field in model_dict:
                model_dict['ext'][field] = model_dict[field]
                del model_dict[field]

        # 下载模型
        if model.source_channel != SourceChannel.LOCAL_PATH:
            from src.apps import job_curd
            image = settings.MODEL_DOWNLOAD_IMG
            if not image:
                raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields="下载模型镜像")
            if image.startswith('/'):
                image = f'{settings.DOCKER_REGISTRY}{image}'
            download_jobs = await job_curd.get_by_filter([
                {'column_name': 'remark', 'value': model.source_model},
                {'column_name': 'action', 'value': JobAction.ModelDownload.value},
                {'column_name': 'status', 'value': JobStatus.RUNNING.value}
            ])
            if download_jobs:
                raise MaaSBaseException(Err.ILLEGAL_STATUS_OPERATION, resource_id=model.source_model, status=ModelStatus.DOWNLOADING.value)
            job_id = await job_curd.submit_job(resource_id=model.id, action=JobAction.ModelDownload.value,
                                      resource_transition_status=ModelStatus.DOWNLOADING.value, remark=model.source_model)
            create_download_model_job(model.source_channel, model.id, model.source_model, image, job_id)
            model_dict['status'] = ModelStatus.PENDING.value
        self.session.add(transform_to_model(self.ModelT, model_dict, DataOper.CREATE))
        self.save(model)
        return model.id

    @session_manage()
    async def update(self, model_id, model: ModelCreateOrUpdate):
        old_model = await self.get_by_id(model_id)
        if old_model.name != model.name:
            await self._valid_name(model.name, old_model.provider)
        tag_curd.save_resource_tag(model_id, model.tags)
        model_dict = model.model_dump(exclude_unset=True, exclude={"inference_engine"})
        model_dict = pydash.pick(model_dict, UPDATE_FIELDS)
        if model_dict:
            await self.base_update(model_id, model_dict, strict=True)
        self.save(model)

    @session_manage()
    async def delete(self, model_id):
        tag_curd.save_resource_tag(model_id, [])
        await self.base_update(model_id, {"status": ModelStatus.DELETED.value})

    @staticmethod
    def fill_model_tags(models: list[ModelDetail | Model]):
        model_ids = [model.id for model in models]
        tag_dict = tag_curd.get_tag_by_resource(model_ids)
        for model in models:
            if model.id in tag_dict:
                model.model_tags = [ModelTag(**tag) for tag in tag_dict[model.id]]

    @session_manage()
    async def fill_model_info(self, models: list[ModelDetail|Model]):
        if not models:
            return

        # 推理引擎
        model_ids = [model.id for model in models]
        engines = await model_inf_engine_curd.get_by_filter([{"column_name": "model_id", "operator": "is_in", "value": model_ids}])
        engine_dict = pydash.group_by(engines, "model_id")

        # 下载进度
        source_models = [model.ext.get('source_model') for model in models if model.status == ModelStatus.DOWNLOADING.value]
        download_progress = await self.download_progress(source_models)

        # 标签
        self.fill_model_tags(models)

        for model in models:
            model.inference_engine = engine_dict.get(model.id, [])
            source_model = model.ext.get('source_model')
            if source_model in download_progress:
                model.download_progress = float(download_progress[source_model].get('total_progress', {}).get('progress', 0))

    @staticmethod
    async def download_progress(source_models: list[str]) -> dict[str, dict]:
        progress_dict = {}
        for source_model in source_models:
            progress_file = os.path.join(PUBLIC_MODEL_DIR, source_model, 'download.json')
            if os.path.exists(progress_file):
                with open(progress_file, 'r') as f:
                    progress_dict[source_model] = json.load(f)
        return progress_dict

class ModelInfEngineCURD(BaseCURD[ModelInfEngine]):
    ...

model_curd = ModelCURD()
model_inf_engine_curd = ModelInfEngineCURD()
