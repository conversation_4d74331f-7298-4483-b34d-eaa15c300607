# -*- coding: utf-8 -*-
from typing import Optional, Any

from pydantic import BaseModel

from src.common.const.comm_const import DEF_PAGE_SIZE, MAX_PAGE_SIZE


class BaseReq(BaseModel):
    """基础请求 model"""
    ...


class BaseIdReq(BaseReq):
    id: int


class BaseStrIdReq(BaseReq):
    id: str


class BasePageReq(BaseReq):
    offset: Optional[int] = 0
    size: Optional[int] = DEF_PAGE_SIZE

    def __init__(__pydantic_self__, **data: Any) -> None:
        super().__init__(**data)
        _self = __pydantic_self__
        _self.offset = _self.offset or 0
        _self.size = _self.size or DEF_PAGE_SIZE
        _self.size = min(_self.size, MAX_PAGE_SIZE)
