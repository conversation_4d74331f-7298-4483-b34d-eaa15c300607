# -*- coding: utf-8 -*-
from statistics import mean

from src.apps.inference_service.rsp_schema import InfSvc
from src.system.integrations.monitoring.MetricOperator import metric_operator


class MonitoringCURD:
    # 空指标数据的默认值
    EMPTY_METRIC = {
        "vals": [],
        "max_value": 0,
        "min_value": 0,
        "avg_value": 0,
        "current_value": 0
    }

    def _calculate_time_series_stats(self, time_value):
        """计算时间序列的统计值，包括最大值、最小值、平均值和当前值"""
        return self.EMPTY_METRIC if not time_value else {
            "vals": sorted(time_value.items(), key=lambda x: x[0]),
            "max_value": max(time_value.values()),
            "min_value": min(time_value.values()),
            "avg_value": mean(time_value.values()),
            "current_value": list(time_value.values())[-1]
        }

    def _float_value(self, data):
        for v in data:
            v[1] = round(float(v[1]), 3)
        return data

    def query_metrics(self, options, service: InfSvc):
        res = metric_operator.query_metrics(options, service)
        metric_data = {"pod_memory_usage_wo_cache": [],
                       "pod_cpu_usage": [],
                       "pod_net_bytes_transmitted": [],
                       "pod_net_bytes_received": [],
                       "http_response_total": [],
                       "qps": [],
                       "pod_gpu_util": [],
                       "pod_gpu_mem_usage": []
                       }
        for metric in ["pod_net_bytes_transmitted", "pod_net_bytes_received", "qps", "http_response_total"]:
            if res.get(metric, {}).get("result", []):
                metric_data.update({metric: self._float_value(res[metric]["result"][0]["values"])})

        for metric in ["pod_memory_usage_wo_cache", "pod_cpu_usage", "pod_gpu_util", "pod_gpu_mem_usage"]:
            if not res.get(metric, {}).get("result", []):
                continue
                
            data = res[metric]
            pod_field = "exported_pod" if metric in ["pod_gpu_util", "pod_gpu_mem_usage"] else "pod"
            
            time_range = []
            for res_item in data["result"]:
                for v in res_item["values"]:
                    if v[0] not in time_range:
                        time_range.append(v[0])
            time_range.sort()
            
            metric_res = {}
            service_time_value = {}
            
            for pod_data in data["result"]:
                pod_id = pod_data["metric"][pod_field]
                time_value = {}
                
                for v in pod_data["values"]:
                    time_value[v[0]] = round(float(v[1]), 3)
                    service_time_value.setdefault(v[0], []).append(round(float(v[1]), 3))
                
                for t in time_range:
                    if t not in time_value:
                        time_value[t] = 0
                
                if metric in ["pod_gpu_util", "pod_gpu_mem_usage"]:
                    gpu_id = 'gpu' + pod_data["metric"]["gpu"]
                    item_id = f"{pod_id}-{gpu_id}"
                else:
                    item_id = pod_id
                
                metric_res[item_id] = self._calculate_time_series_stats(time_value)
            
            for k, v in service_time_value.items():
                service_time_value[k] = mean(v)
            
            metric_res[service.id] = self._calculate_time_series_stats(service_time_value)
            
            metric_data[metric] = metric_res

        return metric_data


monitoring_curd = MonitoringCURD()
