from typing import Callable, Optional, Dict, AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker, AsyncEngine
from sqlalchemy.pool import AsyncAdaptedQueuePool


db_engine: AsyncEngine = create_async_engine(
    'postgresql+asyncpg:scott:tiger@localhost/test',
    pool_size=5,
    max_overflow=10,
    pool_recycle=3600,
    pool_pre_ping=True,
    poolclass=AsyncAdaptedQueuePool,
    echo=True,
    echo_pool=True,
    future=True
)
async_session_maker: Callable[..., AsyncSession] = async_sessionmaker(
    db_engine, class_=AsyncSession, expire_on_commit=False, autoflush=False, )


async def get_session(session_args: Optional[Dict]) -> AsyncGenerator[AsyncSession, None]:
    async with async_session_maker(session_args or {}) as session:
        yield session
