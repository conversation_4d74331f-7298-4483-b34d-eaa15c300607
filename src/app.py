# -*- coding: utf-8 -*-
import importlib
import os
import threading
from contextlib import asynccontextmanager

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import Response
from starlette.middleware.cors import CORSMiddleware

from src.apps.inference_service.api import inf_svc_router
from src.common.const.comm_const import API_PREFIX
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger
from src.common.rsp_schema import R
from src.jobs.global_job import global_job
from src.middlewares.logger import LoggerMiddleware
from src.middlewares.user_loader import UserLoaderMiddleware


@asynccontextmanager
async def lifespan(_app: FastAPI):
    if not os.environ.get('ENV_CONF'):
        # 启动全局任务
        threading.Thread(target=global_job.start, args=('model-manage-server',), daemon=True).start()
    yield
    logger.info('清理资源')
    global_job.release()


app = FastAPI(
    title='maas',
    docs_url=f"{API_PREFIX}-docs",
    openapi_url=f"{API_PREFIX}-openapi.json",
    lifespan=lifespan,
)

# 中间件
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])
# if not os.environ.get('ENV_CONF'):
#     app.add_middleware(OperationRecordMiddleware)
app.add_middleware(LoggerMiddleware)
app.add_middleware(UserLoaderMiddleware)


def res_err(exc):
    return Response(R.err(exc).json(), media_type="application/json")


@app.exception_handler(MaaSBaseException)
async def http_exception_handler(request: Request, exc: Exception):
    return res_err(exc)


@app.exception_handler(Exception)
async def http_exception_handler(request: Request, exc: Exception):
    return res_err(exc)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc: Exception):
    return res_err(exc)


app.include_router(inf_svc_router, prefix=API_PREFIX)

APP_PATH = os.path.join(os.path.dirname(__file__), "apps")
for app_name in os.listdir(APP_PATH):
    if os.path.isdir(os.path.join(APP_PATH, app_name)):
        try:
            if not os.path.exists(os.path.join(APP_PATH, app_name, "api.py")):
                continue

            api = importlib.import_module(f"src.apps.{app_name}.api")
            for name in ['api_router', 'admin_router']:
                if hasattr(api, name):
                    logger.info(f"import app.apps.{app_name}.{name} success")
                    app.include_router(getattr(api, name), prefix=API_PREFIX)

        except Exception as e:
            raise e
