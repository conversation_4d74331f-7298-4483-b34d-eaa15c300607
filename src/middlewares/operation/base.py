import json

from src.common.loggers import logger


def get_id_from_json(json_data: str, param_seg="id"):
    """
    从 JSON 数据中提取 `id` 字段。
    :param json_data: JSON 格式的字符串
    :param param_seg: 查询参数的名称
    """
    ids = []
    try:
        # 将 JSON 字符串解析为字典
        data = json.loads(json_data)
        # 检查并提取 `dict` 字段中的 `id`
        if param_seg in data:
            id = data.get(param_seg)
            if id:
                if isinstance(id, list):
                    return id
                else:
                    ids.append(id)
            return ids
        if "data" in data and isinstance(data["data"], dict):
            id = data["data"].get(param_seg)
            if id:
                if isinstance(id, list):
                    return id
                else:
                    ids.append(id)
        if "data" in data and isinstance(data["data"], list):
            for item in data["data"]:
                id = item.get(param_seg)
                if id:
                    ids.append(id)
    except json.JSONDecodeError as e:
        logger.error(f"无法解析 JSON 数据: {e}")
    return ids


def get_ids_from_param(params, param_seg="id"):
    """
    从 URL 的查询参数或路径参数中提取 `id` 字段（多个 ID 以逗号分隔）。
    :param params: 包含查询参数或路径参数的字典
    :param param_seg: 查询参数的名称
    :return: 提取到的 `id` 列表（如果存在）
    """
    try:
        # 获取 id 参数（假设只有一个 id 参数）
        if isinstance(params, dict):
            id_param = params.get(param_seg, [])
        else:
            id_param = params.get(param_seg, [])
            
        if isinstance(id_param, str):
            # 以逗号分隔 id 值，并去除空格
            ids = [id.strip() for id in id_param.split(',')]
        elif isinstance(id_param, list):
            ids = [str(id) for id in id_param]
        else:
            ids = [str(id_param)] if id_param else []
            
        logger.debug(f"Extracted IDs: {ids}")
        return ids
    except Exception as e:
        logger.error(f"无法解析参数: {e}")
        return []

def get_version_id_from_param(params, param_seg="id"):
    """
    从 URL 的查询参数或路径参数中提取 `id` 字段（多个 ID 以逗号分隔）。
    :param params: 包含查询参数或路径参数的字典
    :param param_seg: 查询参数的名称
    :return: 提取到的 `id` 列表（如果存在）
    """
    try:
        model_id = params.get("model_id")
        version_id = params.get("version_id")
        return [model_id + "/" + version_id]
    except Exception as e:
        logger.error(f"无法解析参数: {e}")
        return []


def get_version_id_from_json(json_data: str, param_seg="id"):
    """
    从 JSON 数据中提取 `id` 字段。
    :param json_data: JSON 格式的字符串
    :param param_seg: 查询参数的名称
    """
    ids = []
    try:
        # 将 JSON 字符串解析为字典
        data = json.loads(json_data)
        model_id = data.get("data").get("model_id")
        version_id = data.get("data").get("version_id")
        ids.append(model_id + "/" + version_id)

    except json.JSONDecodeError as e:
        logger.error(f"无法解析 JSON 数据: {e}")
    return ids

def get_pod_id_from_param(params, param_seg="id"):
    """
    从 URL 的查询参数或路径参数中提取 `id` 字段（多个 ID 以逗号分隔）。
    :param params: 包含查询参数或路径参数的字典
    :param param_seg: 查询参数的名称
    :return: 提取到的 `id` 列表（如果存在）
    """
    try:
        service_id = params.get("service_id")
        pod_id = params.get("pod_id")
        return [service_id + "/" + pod_id]
    except Exception as e:
        logger.error(f"无法解析参数: {e}")
        return []