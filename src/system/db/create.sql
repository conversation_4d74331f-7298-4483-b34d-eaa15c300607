-- CREATE DATABASE maas WITH OWNER = aicp ENCODING = 'UTF8';

CREATE TABLE model
(
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    category character varying(255) NOT NULL,
    icon text,
    status character varying(255) NOT NULL,
    latest_version_id character varying(255)  NOT NULL,
    latest_version_name character varying(255)  NOT NULL,
    latest_approved_version_id character varying(255),
    latest_approved_version_name character varying(255),
    description text ,
    brief text ,
    developer character varying(255) NOT NULL,
    agreement text,
    agreement_link text,
    agreement_name character varying(255),
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    creator character varying(255),
    CONSTRAINT model_pkey PRIMARY KEY (id)
);
ALTER TABLE "model" OWNER TO "aicp";

CREATE TABLE tag
(
    id character varying(255) NOT NULL,
    code character varying(255)  NOT NULL,
    name character varying(255) NOT NULL,
    level integer NOT NULL,
    parent_id character varying(255) NOT NULL,
    root_id character varying(255) NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    creator character varying(255) ,
    CONSTRAINT tag_pkey PRIMARY KEY (id)
);
ALTER TABLE "tag" OWNER TO "aicp";

CREATE TABLE model_tag
(
    id serial NOT NULL,
    model_id character varying(255) NOT NULL,
    tag_code character varying(255) NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT model_tag_pkey PRIMARY KEY (id)
);
ALTER TABLE "model_tag" OWNER TO "aicp";

CREATE TABLE image
(
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    namespace character varying(255) NOT NULL,
    repo character varying(255) NOT NULL,
    description text,
    creator character varying(255) NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT maas_image_pkey PRIMARY KEY (id)
);
ALTER TABLE "image" OWNER TO "aicp";

CREATE TABLE image_tag
(
    tag_id character varying(255)  NOT NULL,
    tag_name character varying(255) NOT NULL,
    image_uri text NOT NULL,
    source text,
    image_id character varying(255) NOT NULL,
    description text,
    creator character varying(255) COLLATE pg_catalog."default" NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    scope character varying(255),
    CONSTRAINT maas_image_tag_pkey PRIMARY KEY (tag_id)
);
ALTER TABLE "image_tag" OWNER TO "aicp";

CREATE TABLE image_tag_label
(
    id serial NOT NULL,
    tag_id character varying(255)  NOT NULL,
    category character varying(255) NOT NULL,
    val character varying(255) NOT NULL,
    create_time timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT image_tag_label_pkey PRIMARY KEY (id)
);
ALTER TABLE "image_tag_label" OWNER TO "aicp";

CREATE TABLE job
(
    job_id character varying(255) NOT NULL,
    resource_id character varying(255) NOT NULL,
    action character varying(255) NOT NULL,
    target_replica integer,
    status character varying(255),
    creator character varying(255),
    reason text,
    create_time timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT job_pkey PRIMARY KEY (job_id)
);
ALTER TABLE "job" OWNER TO "aicp";


CREATE TABLE inference_service (
	"service_id" VARCHAR NOT NULL,
	"service_name" VARCHAR(255) DEFAULT '',
	"image_id" VARCHAR(255) NOT NULL,
	"image_tag_id" VARCHAR(255) NOT NULL,
	"creator" VARCHAR(255) NOT NULL,
	"model_id" VARCHAR(255) NOT NULL,
	"model_version_id" VARCHAR(255) NOT NULL,
	"intranet_url" VARCHAR(255) NULL DEFAULT '',
	"external_url" VARCHAR(255) NULL DEFAULT '',
	"create_time" TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
	"update_time" TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
	"model_name" VARCHAR(255) NULL DEFAULT NULL,
	"status" VARCHAR(255) NULL DEFAULT NULL,
	"transition_status" VARCHAR(255) NULL DEFAULT NULL,
	"instance_num" INTEGER NULL DEFAULT NULL,
	"health_status" VARCHAR(255) NULL DEFAULT NULL,
	"resource_group_type" INTEGER NULL DEFAULT 2,
	"resource_group_id" VARCHAR(255) NULL DEFAULT NULL,
	"resource_group_nodes" VARCHAR(255) NULL DEFAULT NULL,
    "api_key" VARCHAR(255) DEFAULT '',
	PRIMARY KEY ("service_id")
);
ALTER TABLE "inference_service" OWNER TO "aicp";


CREATE TABLE inference_service_spec (
	"service_id" VARCHAR(255) NOT NULL,
	"sku_id" VARCHAR(255) NOT NULL,
	"status" VARCHAR(255) NULL DEFAULT '',
	"aipods_type" VARCHAR(255) NULL DEFAULT '',
	"aipods_scope" VARCHAR(255) NULL DEFAULT '',
	"aipods_usage" VARCHAR(255) NULL DEFAULT '',
	"cpu_count" INTEGER NULL DEFAULT '0',
	"memory" INTEGER NULL DEFAULT '0',
	"cpu_model" VARCHAR(255) NULL DEFAULT '',
	"gpu_model" VARCHAR(255) NULL DEFAULT '',
	"gpu_count" INTEGER NULL DEFAULT '0',
	"gpu_memory" INTEGER NULL DEFAULT '0',
	"os_disk" INTEGER NULL DEFAULT '0',
	"disk" INTEGER NULL DEFAULT '0',
	PRIMARY KEY ("service_id", "sku_id")
);
ALTER TABLE "inference_service_spec" OWNER TO "aicp";

CREATE TABLE model_version (
	"version_id" VARCHAR(255) NOT NULL,
	"version_name" VARCHAR(255) NOT NULL,
	"model_id" VARCHAR(255) NOT NULL,
	"model_type" VARCHAR(255) NULL,
	"description" TEXT NULL DEFAULT NULL,
	"status" VARCHAR(255) NULL DEFAULT NULL,
	"creator" VARCHAR(255) NOT NULL,
	"create_time" TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
	"update_time" TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
	"model_url" VARCHAR(255) NOT NULL,
	"config_content" TEXT NULL DEFAULT NULL,
	"requirements" TEXT NULL DEFAULT NULL,
	"environment" TEXT NULL DEFAULT NULL,
	"run_cmd" TEXT NULL DEFAULT NULL,
	"model_format" VARCHAR(255) NULL DEFAULT NULL,
	"model_framework" VARCHAR(255) NULL DEFAULT NULL,
	"port" VARCHAR(255) NULL DEFAULT NULL,
	"image_type" VARCHAR(255) NULL DEFAULT NULL,
	"image_id" VARCHAR(255) NULL DEFAULT NULL,
	"extra" VARCHAR(255) NULL DEFAULT NULL,
	"mount_path" VARCHAR(255) NULL DEFAULT NULL,
	"image_tag_id" VARCHAR(255) NULL DEFAULT '',
	"custom_image" TEXT NULL DEFAULT NULL,
	PRIMARY KEY ("version_id")
);
ALTER TABLE "model_version" OWNER TO "aicp";


INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000000', 'application', '应用场景', 0, '-1', '-1', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000001', 'framework', '框架类型', 0, '-1', '-1', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000010', 'image-understanding', '图像理解', 2, 'tag-000003', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000011', 'semantic segmentation', '语义分割', 2, 'tag-000004', 'tag-000000','admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000012', 'translation', '语言翻译', 2, 'tag-000004', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000013', 'face-recognition', '人脸识别', 2, 'tag-000005', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000014', 'action-recognition', '动作识别', 2, 'tag-000005', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000015', 'tensorflow', 'tensorflow', 2, 'tag-000006', 'tag-000001', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000016', 'pytorch', 'PyTorch', 2, 'tag-000006', 'tag-000001', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000017', 'text-generation', '文本生成', 2, 'tag-000002', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000018', 'audio', '语音', 1, 'tag-000000', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000019', 'auto-speech-recognition', '语音识别', 2, 'tag-0000018', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-0000020', 'text-to-speech', '语音合成', 2, 'tag-0000018', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000002', 'text', '文本', 1, 'tag-000000', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000003', 'image', '图像', 1, 'tag-000000', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000004', 'nlp', '自然语言处理', 1, 'tag-000000', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000005', 'cv', '计算机视觉', 1, 'tag-000000', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000006', 'framework-type', '框架类型', 1, 'tag-000001', 'tag-000001', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000007', 'text-detection', '文本检测', 2, 'tag-000002', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000008', 'text-understanding', '文本理解', 2, 'tag-000002', 'tag-000000', 'admin');
INSERT INTO tag(id, code, name, level, parent_id, root_id, creator) VALUES ('tag-000009', 'image-segmentation', '图像分割', 2, 'tag-000003', 'tag-000000', 'admin');
