# -*- coding: utf-8 -*-
import os
import subprocess

import requests
from fastapi import APIRouter, Depends, Request, Query

from src.apps.depends import check_permission
from src.apps.image.curd import image_curd
from src.apps.model.curd import model_curd
from src.apps.model.req_schema import ModelSquareReq, ModelCreateOrUpdate, ModelReleaseReq
from src.apps.model.rsp_schema import Model
from src.common.const.comm_const import ModelStatus, UserPlat, PUBLIC_MODEL_DIR, SourceChannel
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.dto import QueryDTO, MODEL_WRITE, MODEL_READ
from src.common.dynamic_filter import param_to_query
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger
from src.common.rsp_schema import PageResponse, DataResponse, BaseResponse, IDResponse, ListResponse
from src.common.utils.data import wrap_rsp

api_router = APIRouter(prefix="/api/model", tags=["模型接口"], dependencies=[Depends(check_permission(UserPlat.ALL))])
admin_router = APIRouter(prefix="/admin/model", tags=["模型接口(管理端)"], dependencies=[Depends(check_permission(UserPlat.KS_CONSOLE))])
SQUARE_LIST_FIELD = {"id", "name", "icon", "brief", "developer", "create_time", "provider", "model_tags", "n_params"}


@api_router.get('/square')
async def list_square(req: ModelSquareReq = Query()) -> PageResponse[dict]:
    data, page_total = await model_curd.list_square(req)
    model_curd.fill_model_tags(data)
    ret_list = [row.model_dump(include=SQUARE_LIST_FIELD) for row in data]
    return wrap_rsp(ret_list, total=page_total)


@api_router.get('/square/{model_id}')
async def get_square_model(model_id: str) -> DataResponse[dict]:
    model = await model_curd.get_by_id(model_id)
    if not model or model.status not in [ModelStatus.APPROVED.value]:
        raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{model_id}")
    await model_curd.fill_model_info([model])
    available_fields = SQUARE_LIST_FIELD.copy()
    available_fields.update({"prefer_vram", "deploy_editable", "deploy_type"})
    if model.deploy_editable:
        available_fields.update({"requirements", "port", "environment", "image", "inference_engine"})
    return wrap_rsp(model.model_dump(include=available_fields))


@api_router.get('/channel/models')
async def list_channel_models(request: Request, source_channel: SourceChannel):
    """
    查询渠道模型列表
    :param request:
    :param source_channel:
    :return:
    """
    channel_data = {
        SourceChannel.MODEL_SCOPE.value: {
            "url": "https://www.modelscope.cn/api/v1/dolphin/models",
            "method": "PUT",
            "int_fields": ["PageSize", "PageNumber"],
            "obj_fields": ["Criterion"],
        },
        SourceChannel.HUGGING_FACE.value: {
            "url": "https://huggingface.co/models-json",
            "method": "GET",
        }
    }
    if source_channel not in channel_data:
        raise MaaSBaseException(Err.VALIDATE_PARAMS, message=f"不支持渠道[{source_channel}]")
    _param = dict(request.query_params)
    del _param['source_channel']
    channel = channel_data[source_channel]
    if channel["int_fields"]:
        _param = {key: int(val) if key in channel["int_fields"] else val for key, val in _param.items()}
    if channel["obj_fields"]:
        _param = {key: eval(val) if key in channel["obj_fields"] else val for key, val in _param.items()}
    params = _param if channel["method"] == 'GET' else None
    data = _param if channel["method"] != 'GET' else None
    headers = {"content-type": "application/json"}
    try:
        result = requests.request(channel["method"], channel["url"], params=params, json=data, headers=headers, timeout=30)
        return result.json()
    except Exception:  # noqa
        logger.exception(f"查询渠道[{source_channel}]模型信息失败:")
        raise MaaSBaseException(Err.INTERFACE_FAILED, action=source_channel, message="查询异常")


@api_router.get("/public-model-dirs")
async def public_model_dirs() -> ListResponse[dict]:
    """
    查询公共模型文件目录，返回格式为：
    [{"path": "PUBLIC_MODEL_DIR/vendor", "models": ["model1", "model2"]}]
    :return: List of model directories and their models
    """
    user = Context.USER.get()
    if not user.is_admin:
        return wrap_rsp([])

    result = []
    try:
        # 遍历公共模型目录
        for vendor in os.listdir(PUBLIC_MODEL_DIR):
            vendor_path = os.path.join(PUBLIC_MODEL_DIR, vendor)
            if os.path.isdir(vendor_path) and not vendor_path.startswith('.'):
                # 获取该厂商目录下的所有模型
                models = []
                for model in os.listdir(vendor_path):
                    model_path = os.path.join(vendor_path, model)
                    # 有 .lock 文件代表没下载完整
                    if os.path.isdir(model_path) and not os.path.islink(model_path) and not os.path.exists(os.path.join(model_path, '.lock')):
                        models.append(model)
                if models:
                    result.append({
                        "path": os.path.join(PUBLIC_MODEL_DIR, vendor),
                        "models": models
                    })
    except Exception:  # noqa
        logger.exception(f"获取公共模型目录失败:")

    return wrap_rsp(result)


@api_router.post("")
async def create_model(model: ModelCreateOrUpdate) -> IDResponse[str]:
    ret = await model_curd.create(model)
    return wrap_rsp(ret)


@api_router.put("/{model_id}", dependencies=[Depends(check_permission(valid_owner=MODEL_WRITE))])
async def update_model(model_id, model: ModelCreateOrUpdate) -> BaseResponse:
    model.id = model_id
    await model_curd.update(model_id, model)
    return wrap_rsp()


@api_router.get("/engines")
async def engine_list() -> ListResponse[dict]:
    id_img_dict = image_curd.engine_img()
    return wrap_rsp([{'id': _id, 'run_cmd': img.run_cmd} for _id, img in id_img_dict.items()])


@api_router.get("/download-progress")
async def download_progress(source_model: str) -> DataResponse[dict]:
    source_model = source_model.replace('|', '/')
    progress_dict = await model_curd.download_progress([source_model])
    return wrap_rsp(progress_dict.get(source_model, {}))


@param_to_query(
    keyword_fields=["id", "name"],
    eq=["status"],
    sort_fields=["create_time", "update_time"],
    table_type=Model,
    exclude_status=[ModelStatus.DELETED.value],
    filter_creator='SUB_ACCOUNT',
)
async def list_param(request: Request):
    ...


@api_router.get("")
async def list_(query_dto: QueryDTO = Depends(list_param)) -> PageResponse[Model]:
    _list, total = await model_curd.get_by_query_dto(query_dto, with_count=True)
    await model_curd.fill_model_info(_list)
    return wrap_rsp(_list, total=total)


@api_router.get("/{model_id}", dependencies=[Depends(check_permission(valid_owner=MODEL_READ))])
async def get(model_id: str) -> DataResponse[Model]:
    data = await model_curd.get_by_filter([{"column_name": "id", "value": model_id}, {"column_name": "status", "operator": "ne", "value": ModelStatus.DELETED.value}])
    if not data:
        raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{model_id}")
    await model_curd.fill_model_info(data)
    return wrap_rsp(data[0])


@api_router.delete("/{model_id}", dependencies=[Depends(check_permission(valid_owner=MODEL_WRITE))])
async def delete(model_id: str) -> BaseResponse:
    model = await model_curd.get_by_id(model_id)
    if model.status in [ModelStatus.DELETED.value, ModelStatus.APPROVED.value]:
        raise MaaSBaseException(Err.ILLEGAL_STATUS_OPERATION, resource_id=model_id, status=model.status)
    await model_curd.delete(model_id)
    return wrap_rsp()


@admin_router.post("/release")
async def release(req: ModelReleaseReq) -> BaseResponse:
    model = await model_curd.get_by_id(req.model_id)
    if not model:
        raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{req.model_id}")
    if model.status not in [ModelStatus.DRAFT.value, ModelStatus.SUBMITTED.value, ModelStatus.SUSPENDED.value]:
        raise MaaSBaseException(Err.ILLEGAL_STATUS_OPERATION, resource_id=req.model_id, status=model.status)
    model_path = model.mounts[0].get('host_path')
    # 获取目录总大小，G为单位
    if not req.size and os.path.exists(model_path):
        result = subprocess.run(["du", "-sb", model_path], capture_output=True, text=True)
        size_bytes = int(result.stdout.split()[0])
        req.size = size_bytes // (1024 ** 3)
    await model_curd.base_update(req.model_id, {'status': ModelStatus.APPROVED.value, 'size': req.size, 'provider': req.provider, 'developer': req.developer})
    return wrap_rsp()