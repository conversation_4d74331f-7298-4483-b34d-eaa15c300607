# -*- coding: utf-8 -*-
import kr8s
from fastapi import APIRouter, Depends

from src.apps.depends import check_permission
from src.apps.inference_service.curd import inf_svc_curd
from src.apps.monitoring.curd import monitoring_curd
from src.apps.monitoring.req_schema import InferServiceMeterQuerySchema
from src.common.const.comm_const import UserPlat
from src.common.utils.data import wrap_rsp
from src.system.integrations.logging.client import opensearch_client

api_router = APIRouter(prefix="/api/monitoring", tags=["监控"], dependencies=[Depends(check_permission(UserPlat.ALL))])


@api_router.get("/{service_id}/metrics")
async def get_infer_service_metrics(service_id: str, query: InferServiceMeterQuerySchema = Depends(InferServiceMeterQuerySchema)):
    service = await inf_svc_curd.get_by_id(service_id)
    namespace = service.creator.lower()
    pods = kr8s.get("pods", namespace=namespace, label_selector=f"app={service_id}")

    if query.start_time and service.create_time.timestamp() > query.start_time:
        query.start_time = int(service.create_time.timestamp())
    if not query.step and query.start_time and query.end_time:
        query.step = (query.end_time - query.start_time) // 250

    query.step = max(query.step, 1)
    query.namespace = namespace
    query.pod_filter = [v.name for v in pods]
    query.service_filter = [service_id]

    pod_info = opensearch_client.get_all_pod(service_id, namespace)
    query.full_pod_filter = [v["metadata"]["name"] for v in pod_info]
    if not query.full_pod_filter:
        query.full_pod_filter = query.pod_filter

    res = monitoring_curd.query_metrics(query, service)
    return wrap_rsp(res)
