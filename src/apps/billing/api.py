# -*- coding: utf-8 -*-

from typing import Optional, List

from fastapi import APIRouter, Query, Depends

from src.apps.billing.curd import BillingCURD
from src.apps.billing.req_schema import GetChargeReq
from src.apps.billing.rsp_schema import <PERSON>seIn<PERSON>, Price, ChargeData
from src.apps.depends import check_permission
from src.apps.inference_service.curd import inf_svc_curd
from src.common.const.comm_const import BillingAction, JobReason
from src.common.const.err_const import Err
from src.common.dto import INF_READ
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger
from src.common.rsp_schema import DataResponse, ListResponse
from src.common.utils.data import wrap_rsp, replace_to_utc_str
from src.system.interface import PI

admin_router = APIRouter(prefix="/admin/billing", tags=["billing 接口"])
api_router = APIRouter(prefix="/api/billing", tags=["billing 接口"])


async def billing_action(resource_ids: list[str], func, reason: str):
    ret_list = []
    for service_id in resource_ids:
        status = 'success'
        try:
            await func(service_id, reason=reason, user="billing")
        except Exception:  # noqa
            logger.exception(f'[计费] 资源 [{service_id}][{reason}] 操作失败:')
            status = 'failed'
        ret_list.append({"resource_id": f"qai-{service_id}", "status": status})
    return ret_list

@admin_router.get("/action")
async def action(
        action_type: str = Query(..., description="操作类型, 支持describe, terminate"),
        resource_ids: List[str] = Query(..., min_length=1, description="资源id列表, qai-<resource_id>"),
):
    resource_ids = [PI.product_interface.get_resource_id(product_id) for product_id in resource_ids]
    logger.debug(f"billing request {action_type} {resource_ids}")
    if action_type == BillingAction.DESCRIBE:
        data = await inf_svc_curd.get_by_filter([{"column_name": "id", "operator": "is_in", "value": resource_ids}])
        ret_list = [{'resource_id': f"qai-{v.id}", 'status': v.status, 'name': v.name,
                     'status_time': replace_to_utc_str(v.update_time)} for v in data]
    elif action_type == BillingAction.CEASE:
        ret_list = await billing_action(resource_ids, inf_svc_curd.delete, JobReason.ARREARS_CEASE)
    elif action_type == BillingAction.SUSPEND:
        ret_list = await billing_action(resource_ids, inf_svc_curd.stop, JobReason.ARREARS_SUSPEND)
    elif action_type == BillingAction.RESUME:
        ret_list = await billing_action(resource_ids, inf_svc_curd.start, JobReason.ARREARS_RESUME)
    else:
        raise MaaSBaseException(Err.NOT_IMPLEMENT)
    logger.debug(f"billing response: {ret_list}")
    return {
        "ret_code": 0,
        "message": "success",
        "data": ret_list,
        "counts": len(ret_list)
    }


@api_router.get("/lease-info/{resource_id}", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def lease_info(resource_id: str) -> DataResponse[LeaseInfo]:
    return wrap_rsp(await BillingCURD.get_lease_info(resource_id))


@api_router.get("/get-price")
async def get_price(sku_id: str, replica: int, duration: Optional[int] = None, charge_mode: Optional[str] = None) \
        -> ListResponse[Price]:
    return wrap_rsp(await BillingCURD.get_price(sku_id, replica, duration, charge_mode))


@api_router.get("/get-charge-records", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def get_charge_records(charge_req: GetChargeReq = Depends()) -> DataResponse[ChargeData]:
    return wrap_rsp(await BillingCURD.get_charge_records(charge_req))


@admin_router.get("/lease-info/{resource_id}")
async def lease_info(resource_id: str, user_id: str) -> DataResponse[LeaseInfo]:
    return wrap_rsp(await BillingCURD.get_lease_info(resource_id, user_id))


@admin_router.get("/get-price")
async def get_price(sku_id: str, replica: int, duration: Optional[int] = None, charge_mode: Optional[str] = None) \
        -> ListResponse[Price]:
    return wrap_rsp(await BillingCURD.get_price(sku_id, replica, duration, charge_mode))


@admin_router.get("/get-charge-records")
async def get_charge_records(charge_req: GetChargeReq = Depends()) -> DataResponse[ChargeData]:
    return wrap_rsp(await BillingCURD.get_charge_records(charge_req))
