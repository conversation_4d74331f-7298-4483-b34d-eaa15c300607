apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: model-manage-service-cluster-role
rules:
  - apiGroups:
      - ""
    resources:
      - namespaces
      - nodes
      - pods
      - pods/log
      - pods/exec
      - services
      - deployments
      - persistentvolumeclaims
      - persistentvolumes
      - secrets
      - events
      - configmaps
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update
  - apiGroups:
      - apps
    resources:
      - deployments
      - replicasets
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update
  - apiGroups:
      - networking.istio.io
    resources:
      - virtualservices
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - authorizationpolicies
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update
  - apiGroups:
      - leaderworkerset.x-k8s.io
    resources:
      - leaderworkersets
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update
  - apiGroups:
      - batch
    resources:
      - jobs
      - cronjobs
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
      - update