# -*- coding: utf-8 -*-
import os

import pydash
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from src.common.const.comm_const import UserPlat, API_PREFIX
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.dto import KS_ADMIN_USER, User, EMPTY_USER, Permissions
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger
from src.setting import settings
from src.system.integrations.aicpserver import aicp_client
from src.system.interface import PI

# 没有传用户信息的接口（集群内部调用）
# 计费触发的动作(aicp服务转发)、auth服务查询推理的apikey
NO_AUTH_PATH = ['/maas/admin/billing/action', '/maas/api/inf-svc/auth', '/healthz', '/openapi.json']


class UserLoaderMiddleware(BaseHTTPMiddleware):
    """
    获取用户信息并设置到上下文中
    """

    async def dispatch(self, request: Request, call_next):

        if os.getenv("ENV_CONF", None) == "dev":
            if request.url.path.startswith(f'{API_PREFIX}/admin'):
                request.headers._list.append((b"x-remote-group", b"system:authenticated"))
                request.headers._list.append((b"x-remote-user", b"admin"))
            elif not request.headers.get("aicp-userid"):
                request.headers._list.append((b"aicp-userid", settings.MOCK_USER.encode()))

        # 完全不需要权限的接口
        if request.url.path in NO_AUTH_PATH:
            user = EMPTY_USER
        elif request.headers.get("aicp-userid"):
            # console 端请求 header 中有 aicp-userid，即云平台账户
            user_id = request.headers.get("aicp-userid")
            # 从团队管理服务获取用户及权限信息
            try:
                user_dict: dict = aicp_client.send_request(settings.PROXY_SERVER_HOST, '/global/team/api/user/auth',
                                                           user_id=user_id, auth_type=aicp_client.AuthType.SIGNATURE)[
                    "data"]
                user = User(**user_dict)
            except Exception as e:
                logger.error("查询用户权限信息失败，使用默认权限:", e)
                user = PI.user_interface.get_user_by_id(user_id)
                user.sub_acc_consume = 0
                user.permissions = [Permissions(**{"module": "INF", "permission": "OWN"})]
            user.plat = UserPlat.QC_CONSOLE

            # 校验子账户是否有模块权限
            permission: Permissions = pydash.find(user.permissions, lambda x: x.module == 'INF')
            sub_user_ids = pydash.pluck(user.sub_users, 'id')
            if user.user_type == 1:
                if permission.permission == 'NO':
                    raise MaaSBaseException(Err.AUTH_INSUFFICIENT)
                elif permission.permission == 'OWN':
                    user.list_user_ids = [user_id]
                elif permission.permission in ('READALL', 'ALL'):
                    user.list_user_ids = sub_user_ids + [user.root_user_id]
            else:
                user.list_user_ids = sub_user_ids + [user_id]
        elif request.headers.get("X-Remote-Group") == "system:authenticated" and request.headers.get("X-Remote-User") == "admin":
            # 管理端(KS) 请求 header 中有 X-Remote-Group 和 X-Remote-User
            user = KS_ADMIN_USER
        else:
            logger.warning(f'请求[{request.url.path}]缺少登录用户信息')
            raise MaaSBaseException(Err.NOT_LOGIN)

        token = Context.USER.set(user)
        try:
            return await call_next(request)
        finally:
            Context.USER.reset(token)
