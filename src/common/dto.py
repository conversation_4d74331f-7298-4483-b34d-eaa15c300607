# -*- coding: utf-8 -*-
from dataclasses import dataclass
from typing import Optional

from pydantic import Field
from pydantic.main import BaseModel
from sqlalchemy import <PERSON>oleanClauseList, UnaryExpression

from src.common.const.comm_const import UserPlat, AuthType
from src.common.req_schema import BasePageReq
from src.setting import settings


class IdName(BaseModel):
    id: str = ''
    name: str = ''

class Permissions(BaseModel):
    module: str = ''
    module_name: Optional[str] = ''
    permission: str = ''  # NG-容器实例；TN-训练任务；INF-推理服务；EPFS-存储与数据；RG-专属资源；
    role_id: str = ''

class User(BaseModel):
    user_id: str = ''
    user_name: str = ''
    plat: Optional[UserPlat] = None
    root_user_id: str = ''
    user_type: int = 0  # 0-主账号，1-子账号
    status: str = ''
    email: str = ''
    notify_email: str = ''
    phone: str = ''
    owner: Optional[str] = ''
    is_admin: Optional[bool] = False
    sub_acc_consume: int = -1  # -1: 不允许；1：允许
    sub_users: list[IdName] = Field(default_factory=list)
    roles: list[IdName] = Field(default_factory=list)
    groups: list[IdName] = Field(default_factory=list)
    permissions: list[Permissions] = Field(default_factory=list)

    list_user_ids: list[str] = Field(default_factory=list)  # 用来在查询列表接口中过滤

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        self.owner = self.user_id
        self.is_admin = self.user_id in settings.SUPER_ACCOUNTS


EMPTY_USER = User()
KS_ADMIN_USER = User(**{'user_id': 'admin', 'user_name': 'admin', 'plat': UserPlat.KS_CONSOLE})


class QueryDTO:

    express: BooleanClauseList
    page: BasePageReq
    sort_expr: UnaryExpression

    def __init__(self, express: BooleanClauseList, page, offset, size, sort_expr) -> None:
        self.express = express
        self.page = BasePageReq(page=page, offset=offset, size=size)
        self.sort_expr = sort_expr


@dataclass
class AccessKey:
    access_key_id: str
    secret_access_key: str


class ValidOwner:
    tb_name: str
    tb_id_field: str
    tb_user_field: str

    auth_type: AuthType

    # 业务id字段，获取顺序 path > params > body
    biz_id_field: list[str]

    def __init__(self, tb_name: str, biz_id_field: list[str], tb_id_field: str = 'id', tb_user_field: str = 'creator', auth_type: AuthType = AuthType.READ) -> None:
        self.tb_name = tb_name
        self.tb_id_field = tb_id_field
        self.tb_user_field = tb_user_field
        self.biz_id_field = biz_id_field
        self.auth_type = auth_type


INF_READ = ValidOwner('inference_service', ['service_id', 'resource_id'] )
INF_WRITE = ValidOwner('inference_service', ['service_id', 'resource_id'], auth_type=AuthType.WRITE)
MODEL_READ = ValidOwner('model', ['model_id'])
MODEL_WRITE = ValidOwner('model', ['model_id'], auth_type=AuthType.WRITE)