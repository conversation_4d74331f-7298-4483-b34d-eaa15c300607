# -*- coding: utf-8 -*-
import asyncio
import os
from datetime import datetime
from time import sleep

from src.apps import job_curd, model_curd
from src.apps.inference_service.curd import inf_svc_curd
from src.common.const.comm_const import INF_EVENT_QUEUE, InfStatus, InfHealthStatus, AlertReason, \
    MODEL_DOWNLOAD_EVENT_QUEUE, JobStatus, MODEL_DOWNLOAD_PROGRESS_QUEUE, ModelStatus
from src.common.loggers import task_logger as logger
from src.jobs.global_job import global_task
from src.setting import settings
from src.system.integrations.cache.redis_client import redis_client
from src.system.integrations.push.push_client import push_client
from src.system.interface import PI

INF_EVENT_CONSUME_GROUP = "inf_event_consume_group"
MODEL_EVENT_CONSUME_GROUP = "model_event_consume_group"
MODEL_PROGRESS_CONSUME_GROUP = "model_progress_consume_group"


def _consume_event(queue, group, process):
    sleep(2)
    consumer_name = os.getenv('HOSTNAME') or 'DEFAULT_CONSUMER'
    redis_client.init_consume_group(queue, group)
    while True:
        try:
            messages = redis_client.consume_msg(queue, group, consumer_name)
            if not messages:
                continue
        except Exception:  # noqa
            logger.exception(f'从队列[{group}]中消费失败：')
            sleep(5)
            continue

        success_msg_ids = []
        for message_id, data in messages:
            try:
                asyncio.run(process(data))
                success_msg_ids.append(message_id)
            except Exception:  # noqa
                logger.exception(f'处理事件[{data}]失败：')

        try:
            if success_msg_ids:
                logger.info(f'成功消费消息:{success_msg_ids}')
                redis_client.ack_msg(queue, group, success_msg_ids)
        except Exception:  # noqa
            logger.exception(f'确认消息[{success_msg_ids}]失败：')
        sleep(2)


@global_task('消费推理服务资源事件', async_exec=True)
def consume_inf_event():
    _consume_event(INF_EVENT_QUEUE, INF_EVENT_CONSUME_GROUP, update_inf_status)


@global_task('消费模型下载事件', async_exec=True)
def consume_model_download_event():
    _consume_event(MODEL_DOWNLOAD_EVENT_QUEUE, MODEL_EVENT_CONSUME_GROUP, model_download_event)


@global_task('消费模型下载进度事件', async_exec=True)
def consume_model_download_progress():
    _consume_event(MODEL_DOWNLOAD_PROGRESS_QUEUE, MODEL_PROGRESS_CONSUME_GROUP, model_download_progress)


async def model_download_event(data: dict):
    """
    模型下载相关事件消费
    :param data: 消息 body
    :return:
    """
    job_id, status, model_id, reason, model_name = data['job_id'], data['status'], data['model_id'], data['remark'], data['model_name']
    model_status = ''
    job_status = ''
    if status == JobStatus.RUNNING.value:
        model_status = ModelStatus.DOWNLOADING.value
    if status == JobStatus.FAILED.value:
        model_status = ModelStatus.DOWNLOAD_FAILED.value
        job_status = JobStatus.FAILED.value
    if status == JobStatus.SUCCESS.value:
        model_status = ModelStatus.DRAFT.value
        job_status = JobStatus.SUCCESS.value

    if model_status:
        await model_curd.base_update(model_id, {'status': model_status})
    if job_status:
        await job_curd.base_update(job_id, {'status': job_status, 'reason': reason})
    logger.warning(f"模型下载事件 [{job_id} | {model_id} | {model_name}] [{status} | {reason}]")


async def model_download_progress(data: dict):
    """
     模型下载进度事件消费
    :param data: 消息 body
    :return:
    """
    model_id, model_name, progress, job_id = data['model_id'], data['model_name'], data['progress'], data['job_id']
    job = await job_curd.get_by_id(job_id)
    push_client.push_socket(job.action, job.resource_id, JobStatus.RUNNING.value, [job.creator], op_id=job.job_id, resource_type='model', reason=progress)


async def update_inf_status(data: dict):
    """
    检测运行服务的健康状态
    """
    service_id = data['service_id']
    infer_service = await inf_svc_curd.get_by_id(service_id)
    if not infer_service or infer_service.transition_status or infer_service.status != InfStatus.ACTIVE.value:
        return

    health: bool = int(data['unavailable_replicas']) == 0
    health_status = InfHealthStatus.HEALTH.value if health else InfHealthStatus.UNHEALTH.value
    key = f'{service_id}-update-count'
    if infer_service.health_status != health_status:
        redis_client.incr_exp(key)

    await inf_svc_curd.base_update(service_id, {'health_status': health_status})
    if health:
        return

    alarm_msg = ''
    # 不健康持续一段时间需要告警
    if (datetime.now() - infer_service.update_time).seconds > settings.UNHEALTH_STATUS_THRESHOLD_TIME:
        alarm_msg = f'推理服务 {service_id} 不健康'

    # 60秒内状态更新超过阈值报警
    if int(redis_client.get(key) or 0) > settings.INFER_SERVICE_UPDATE_STATUS_THRESHOLD:
        alarm_msg = f'推理服务 {service_id} 状态不稳定'

    if alarm_msg:
        push_client.send_maas_alert_event(AlertReason.MaaSUnhealthInferService, message=alarm_msg)
        user = PI.user_interface.get_user_by_id(infer_service.creator)
        push_client.push_msg_to_user("aicp_maas_error", infer_service.creator, {
            'username': user.user_name,
            'zone': settings.QINGCLOUD_ZONE,
            'id': service_id,
            'action': '处于异常中'
        })
        logger.warning(f'推理服务[{service_id}]健康状态不正常, 不可用副本数[{data["unavailable_replicas"]}]')
