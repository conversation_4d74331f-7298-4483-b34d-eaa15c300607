# -*- coding: utf-8 -*-
import re
from typing import Optional

from pydantic import BaseModel, Field, field_validator, model_validator

from src.apps.image.curd import image_curd
from src.common.const.comm_const import SourceChannel, ImageType, PUBLIC_MAAS_DIR_NAME, PUBLIC_MODEL_DIR
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.exceptions import MaaSBaseException
from src.common.req_schema import BasePageReq
from src.common.utils.data import validate_port, validate_storage_owner


class ModelMount(BaseModel):
    host_path: str = Field(..., max_length=1024)
    mount_path: str = Field(..., max_length=1024)


class ModelImage(BaseModel):
    type: ImageType
    image: str = Field(default=None, max_length=1024)
    username: Optional[str] = Field(default=None, max_length=1024)
    password: Optional[str] = Field(default=None, max_length=1024)
    run_cmd: Optional[str] = Field(default=None, max_length=10240)


class InferenceEngine(BaseModel):
    engine_id: str = Field(..., max_length=64)
    run_cmd: str = Field(default="", max_length=10240)


class ModelSquareReq(BasePageReq):
    keyword: Optional[str] = ''
    tags: Optional[list[str]] = []


class ModelCreateOrUpdate(BaseModel):
    id: Optional[str] = None
    name: str = Field(default="", max_length=64)
    icon: str = Field(default="", max_length=10240)
    brief: str = Field(default="", max_length=10240)
    tags: Optional[list[str]] = []
    n_params: Optional[float] = 0

    requirements: str = Field(default="", max_length=1024)
    port: int = Field(default=8000, ge=1, le=65535)
    environment: str = Field(default="", max_length=10240)
    source_channel: Optional[SourceChannel] = 'local_path'
    source_model: Optional[str] = ''
    mounts: list[ModelMount] = Field(..., min_length=0, max_length=1)
    image: Optional[ModelImage] = None
    inference_engine: Optional[list[InferenceEngine]] = None
    deploy_editable: int = Field(default=0, ge=0, le=1)

    @field_validator('port')
    def check_port(cls, v):  # noqa
        validate_port(v)
        return v

    @field_validator('name')
    def check_name(cls, v):  # noqa
        pattern = r'^[\u4e00-\u9fa5a-zA-Z0-9_\-\.]+$'
        if not bool(re.fullmatch(pattern, v)):
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="模型名称不合法")
        return v

    @field_validator('tags')
    def check_tags(cls, v):  # noqa
        v = v or []
        if len(set(v)) != len(v):
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="模型标签不能重复")
        return v

    @model_validator(mode='after')
    def validate_req(self) -> 'ModelCreateOrUpdate':
        if not self.inference_engine and not self.image:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="推理引擎或镜像必填")
        if not self.inference_engine:
            return self

        # 校验推理引擎
        engine_ids = [engine.engine_id for engine in self.inference_engine]
        if len(set(engine_ids)) != len(engine_ids):
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="推理引擎不能重复")

        available_engines = list(image_curd.engine_img().keys())
        unavailable_engines = set(engine_ids) - set(available_engines)
        if unavailable_engines:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message=f"推理引擎[{', '.join(unavailable_engines)}]或镜像不存在")

        # 校验挂载
        user = Context.USER.get()
        if self.source_channel != SourceChannel.LOCAL_PATH:
            if not self.source_model:
                raise MaaSBaseException(Err.VALIDATE_PARAMS, message="缺少模型 id")
            if not user.is_admin:
                raise MaaSBaseException(Err.AUTH_INSUFFICIENT)

            # 设置默认的模型文件目录
            if not self.mounts:
                self.mounts = [ModelMount(**{
                    "host_path": f"{PUBLIC_MODEL_DIR}/{self.source_model}",
                    "mount_path": f"/model/{self.source_model}"
                })]
            else:
                self.mounts[0].host_path = f"{PUBLIC_MODEL_DIR}/{self.source_model}"
        elif not self.mounts:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="缺少模型挂载目录")

        host_path_set = set()
        for mount in self.mounts:
            if not mount.host_path.startswith('/'):
                raise MaaSBaseException(Err.VALIDATE_PARAMS, message="模型路径必须以/开头")
            root_name = mount.host_path.split('/')[1]
            if root_name == PUBLIC_MAAS_DIR_NAME:
                if not user.is_admin:
                    raise MaaSBaseException(Err.VALIDATE_PARAMS, message="没有模型公共目录权限")
            else:
                host_path_set.add(root_name)
        validate_storage_owner(list(host_path_set))

        return self


class ModelReleaseReq(BaseModel):
    model_id: str
    provider: str = Field(..., max_length=64)  # 英文厂商
    developer: str = Field(..., max_length=64)  # 中文厂商
    size: Optional[int] = Field(default=0, ge=0)  # 模型文件大小（G）