# -*- coding: utf-8 -*-
import json

from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException
from src.setting import settings
from src.system.interface.abs_billing_interface import AbsBillingInterface
from src.system.interface.qingcloud.iaas_client import iaas_client


# 云平台计费配置
PRODUCTION_PRODUCT_CODE = "qai"
PRODUCTION_PREFIX = "qai-"
PRODUCTION_SPEC_ID = "aipods"
ZONE = settings.QINGCLOUD_ZONE


def wrap_id(resource_id: str) -> str:
    """
    资源id包装为 billing 约定格式
    :param resource_id: 资源id
    :return: billing产品id
    """
    return PRODUCTION_PREFIX + resource_id


class QingcloudBilling(AbsBillingInterface):

    def get_charge_records(self, resource_id: str, user_id: str, offset: int = 0, limit: int = 20):
        return iaas_client.send_request("GetChargeRecords", {
            "resource": wrap_id(resource_id),
            "user": user_id,
            "zone": ZONE,
            "offset": offset,
            "limit": limit
        })

    def get_price(self, user_id: str, price_info: dict, duration: int = 3600, charge_mode: str = "elastic"):
        price_info.update({"spec_id": PRODUCTION_SPEC_ID})
        lease_info = {
            "user": user_id,
            "resources": [dict(sequence=0, type=PRODUCTION_PRODUCT_CODE, **price_info)],
            "currency": "cny",
            "zone": ZONE,
            "duration": duration,
            "charge_mode": charge_mode
        }
        return iaas_client.send_request("GetPrice", lease_info)

    def get_lease_info(self, resource_id: str, user_id: str):
        return iaas_client.send_request("GetLeaseInfo", {
            "resource": wrap_id(resource_id),
            "user": user_id,
            "zone": ZONE
        })

    def lease(self, resource_id: str, user_id: str, price_info: dict, duration: int = 3600,
              charge_mode: str = "elastic", **kwargs):
        if not resource_id or not user_id or not price_info:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="缺少参数")
        price_info.update({"spec_id": PRODUCTION_SPEC_ID})
        lease_info = {
            "duration": duration,
            "user": user_id,
            "charge_mode": charge_mode,
            "auto_renew": 0,
            "resources": [wrap_id(resource_id)],
            "zone": ZONE,
            "price_info": json.dumps(price_info),
            "is_skip_handle_failure": 1,
        }
        lease_info.update(kwargs)
        return iaas_client.send_request("Lease", lease_info)

    def check_balance(self, resource_id: str, user_id: str, price_info: dict, price_type: str = "new"):
        if not resource_id or not user_id or not price_info:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="缺少参数")
        price_info.update({"spec_id": PRODUCTION_SPEC_ID})
        return iaas_client.send_request("CheckResourcesBalance", {
            "user": user_id,
            "zone": ZONE,
            "duration": 3600,
            "currency": "cny",
            "price_type": price_type,
            "resources": [{
                "sequence": 0,
                "resource_id": wrap_id(resource_id),
                "price_info": json.dumps(price_info)
            }]
        })

    def unlease(self, resource_id: str, user_id: str):
        return iaas_client.send_request("Unlease", {
            "cease": 0,
            "force": 0,
            "zone": ZONE,
            "user": user_id,
            "resources": [wrap_id(resource_id)]
        })

qingcloud_billing = QingcloudBilling()