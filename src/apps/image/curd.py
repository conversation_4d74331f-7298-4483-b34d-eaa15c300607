# -*- coding: utf-8 -*-
import pydash
import requests
from cachetools import TTLCache, cached
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON>Auth

from src.apps.base_curd import BaseCURD
from src.apps.image.rsp_schema import Image, ImageTag
from src.common.loggers import logger
from src.setting import settings


class ImageCURD(BaseCURD[Image]):

    def __init__(self):
        super(ImageCURD, self).__init__()
        self.repo_proxy = settings.REPO_PROXY
        if self.repo_proxy:
            self.api_url = self.repo_proxy['api_url']
            self.auth = HTTPBasicAuth(self.repo_proxy['user'], self.repo_proxy['pass'])
        else:
            self.auth = HTTPBasicAuth(settings.DOCKER_ADMIN_USER, settings.DOCKER_ADMIN_PASSWORD)
            self.api_url = f"{settings.DOCKER_PROTOCOL}://{settings.DOCKER_REGISTRY}/api/v2.0"

    @cached(cache=TTLCache(maxsize=1024, ttl=600))
    def list(self) -> dict[str, Image]:
        """
        获取镜像列表
        :return:
        """
        images: [str, Image] = {}
        repo_infos = [{'id': item['id'], 'repo': item['repository'], 'type': 'engine', 'run_cmd': item.get('run_cmd', '')}
                      for item in settings.INF_ENGINE.values() if item.get('status') == 'active']
        repo_infos.append(*pydash.map_(settings.PUB_INF_IMG_REPO, lambda item: {'id': item, 'repo': item}))
        for repo_info in repo_infos:
            img_id = repo_info['id']
            repo = repo_info['repo']
            if self.repo_proxy:
                url = f"{self.api_url}/projects/{self.repo_proxy['project']}/repositories/{repo.replace('/', '%252F')}"
            else:
                repo_ = f"{settings.REPO_PREFIX}/{repo}".replace('/', '%252F')
                url = f"{self.api_url}/projects/{settings.REPO_PROJECT}/repositories/{repo_}"
            url += '/artifacts?page=1&page_size=100'
            try:
                rsp = requests.get(url, auth=self.auth, verify=False)
                if rsp and rsp.status_code == 200:
                    artifacts = rsp.json()
                    artifacts = pydash.filter_(artifacts, lambda x: x['tags'])
                    artifacts = pydash.sort_by(artifacts, 'tags[0].push_time', reverse=True)
                    for artifact in artifacts:
                        tag = artifact.get('tags')[0]
                        if img_id not in images:
                            images[img_id] = Image(**{
                                'id': repo_info['id'],
                                'repository': repo,
                                'tags': [],
                                'type': repo_info.get('type', ''),
                                'run_cmd': repo_info.get('run_cmd', '')
                            })
                        images[img_id].tags.append(ImageTag(**{
                            'tag': tag["name"],
                            'image': f'{settings.DOCKER_REGISTRY}/{settings.REPO_PROJECT}/{settings.REPO_PREFIX}/{repo}:{tag["name"]}'
                        }))
                else:
                    logger.warn(f'查询仓库[{repo}]镜像失败: {rsp}')
            except Exception:  # noqa
                logger.exception(f'查询仓库[{repo}]镜像失败')
        return images

    def engine_img(self):
        return {id_: image for id_, image in self.list().items() if image.type == 'engine'}

image_curd = ImageCURD()
