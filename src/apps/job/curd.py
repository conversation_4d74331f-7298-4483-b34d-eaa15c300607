# -*- coding: utf-8 -*-
import datetime
import threading

from src.apps.base_curd import BaseCURD
from src.apps.job.rsp_schema import Job
from src.common.const.comm_const import ResourceModule, JobStatus, PUSH_MODULE
from src.common.context import Context
from src.common.utils.data import transform_to_model, DataOper
from src.common.utils.id_tool import uuid
from src.system.db.sync_db import session_manage
from src.system.integrations.push.push_client import push_client


class JobCURD(BaseCURD[Job]):

    @session_manage()
    async def submit_job(self, resource_id, action, target_replica=0, src_replica=0, resource_transition_status='',
                         reason='', operator=None, owner=None, remark=None):
        # 先判断是否有正在执行的job， 如果有则抢占
        job_id = uuid(ResourceModule.JOB)
        running_job = await self.get_by_filter([{"column_name": "resource_id", "operator": "eq", "value": resource_id},
                                                {"column_name": "status", "operator": "eq",
                                                 "value": JobStatus.RUNNING.value}])
        for job in running_job:
            job.status = JobStatus.SUSPEND.value
            job.reason = f"被{job_id}-{action}抢占"
            job.update_time = datetime.datetime.now()
        self.session.add_all(running_job)

        if not operator:
            operator = Context.USER.get().user_id
        if not owner:
            owner = Context.USER.get().user_id
        data = {
            "job_id": job_id,
            "resource_id": resource_id,
            "action": action,
            "status": JobStatus.RUNNING.value,
            "target_replica": target_replica,
            "src_replica": src_replica,
            "reason": reason,
            "remark": remark,
        }
        job_ins = transform_to_model(self.ModelT, data, fill_comm_fields=DataOper.CREATE)
        job_ins.creator = operator
        self.session.add(job_ins)
        for job in running_job:
            threading.Thread(target=push_client.push_socket,
                             args=(job.action, job.resource_id, JobStatus.FAILED.value, [operator, owner]),
                             kwargs={"op_id": job.job_id,
                                     "resource_type": PUSH_MODULE}).start()
        threading.Thread(target=push_client.push_socket,
                         args=(action, resource_id, resource_transition_status, [operator, owner]),
                         kwargs={"op_id": job_id, "resource_type": PUSH_MODULE}).start()
        return job_id


job_curd = JobCURD()
