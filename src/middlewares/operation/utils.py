# -*- coding: utf-8 -*-
import datetime

from src.common.const.comm_const import API_PREFIX
from src.middlewares.operation.base import get_id_from_json, get_ids_from_param, get_version_id_from_json, \
    get_version_id_from_param, get_pod_id_from_param
from src.middlewares.operation.comm_const import OPERATION
from src.middlewares.operation.reflection_api import url_reflection_type
from src.system.integrations.cache.redis_client import create_redis_data


def remove_prefix(url_path: str, prefix_remove=None) -> str:
    """
    如果前缀不存在于路径开头，则返回原路径。
    :param url_path: 原始路径，例如 "/operation/api/operation"
    :param prefix_remove: 需要去除的prefix
    :return: 去除前缀后的剩余路径，例如 "/api/operation"
    """
    # 确保前缀和路径都是以斜杠开头（标准化）
    prefix_remove_list = [API_PREFIX]
    if prefix_remove:
        prefix_remove_list.append(prefix_remove)
    for prefix in prefix_remove_list:
        # 确保路径以该前缀开头
        if url_path.startswith(prefix):
            # 去掉匹配的前缀部分
            return url_path[len(prefix):] or "/"
    return url_path


def get_resource_id_origin(first_segment, last_segment, method, param=None, json_data=None):
    """
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param param: url的参数
    :param json_data: json数据
    :return:
    """
    # 字典映射参数组合到函数
    func_map = {
        ("model", "model", "POST"): (get_id_from_json, json_data, "model_id"),
        ("model", "model", "DELETE"): (get_ids_from_param, param, "model_id"),
        ("model", "model", "PUT"): (get_ids_from_param, param, "model_id"),
        ("model_version", "model_version", "POST"): (get_version_id_from_json, json_data, "version_id"),
        ("model_version", "model_version", "DELETE"): (get_version_id_from_param, param, "version_id"),
        ("model_version", "model_version", "PUT"): (get_version_id_from_param, param, "version_id"),
        ("inference_service", "inference_service", "POST"): (get_id_from_json, json_data, "service_id"),
        ("inference_service", "inference_service", "DELETE"): (get_ids_from_param, param, "service_id"),
        ("inference_service", "inference_service", "PUT"): (get_id_from_json, param, "service_id"),
        ("inference_service", "start", "PUT"): (get_ids_from_param, param, "service_id"),
        ("inference_service", "stop", "PUT"): (get_ids_from_param, param, "service_id"),
        ("inference_service", "scale", "PUT"): (get_ids_from_param, param, "service_id"),
        ("inference_service", "rebuild", "POST"): (get_pod_id_from_param, param, "service_id"),
    }
    # 根据参数组合获取并调用对应的函数
    values = func_map.get((first_segment, last_segment, method), (get_ids_from_param, param, "request_resource_id"))

    # 将第一个值作为函数，其余作为参数列表
    func, *func_params = values

    # 调用函数并传递参数
    return func(*func_params)

def get_resource_id(first_segment, last_segment, method, param=None, json_data=None):
    """
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param param: url的参数
    :param json_data: json数据
    :return:
    """
    ids = get_resource_id_origin(first_segment, last_segment, method, param, json_data)
    return ids


def generate_resource_redis_data(url, first_segment, last_segment, method, id, user_id, operation_user_id, trace_id):
    """
    :param url: 请求的url
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param id: 资源id
    :param user_id: 用户id
    :param operation_user_id: 操作用户id
    :param trace_id: trace_id
    :return:
    """
    # 字典映射参数组合到函数
    resource_type = url_reflection_type[first_segment]
    operation = OPERATION[first_segment][last_segment + "_" + method]
    start_time = int(datetime.datetime.now().timestamp())
    ret = create_redis_data(user_id, operation_user_id, id, resource_type, operation, start_time, trace_id)
    return ret


def process_list(input_list, prefix):
    # 使用列表推导式处理列表
    return [item[len(prefix):] if item.startswith(prefix) else item for item in input_list]