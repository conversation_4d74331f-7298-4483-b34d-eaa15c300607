# -*- coding: utf-8 -*-
from urllib.parse import urlparse

from pydantic.v1 import BaseSettings


class Settings(BaseSettings):

    # sys
    ERR_LANG: str = "zh"
    LOGGING_LEVEL: str = "INFO"
    BILLING_ENABLE: bool = True
    STORAGE_TYPE: str = "gpfs"
    GPFS_ROOT_DIR = "/share"
    INFERENCE_SERVICE_HOST = ""
    AICP_PLATFORM: str = "coreshub"
    SUPER_ACCOUNTS: list[str] = []
    MOCK_USER = "usr-ohn3PIoq"

    # repo
    DOCKER_REGISTRY: str = ""
    DOCKER_ADMIN_USER: str = "admin"
    DOCKER_ADMIN_PASSWORD: str = "zhu88jie"
    DOCKER_PROTOCOL: str = "https"
    PUB_INF_IMG_REPO = ["pytorch-inference"]
    REPO_PROJECT = "aicp-common"
    REPO_PREFIX = "public"
    REPO_PROXY = {}
    MODEL_DOWNLOAD_IMG: str = ""

    # model
    TAGS = {"txt2txt": "文生文", "txt2img": "文生图", "txt2video": "文生视频", "img2video": "图生视频", "audio": "语音", "embedding": "嵌入", "rerank": "重排序"}
    INF_ENGINE = {
        "vLLM": {"id": "vLLM", "repository": "vllm/vllm-openai", "processor": ["nvidia"], "run_cmd": "python3 -m vllm.entrypoints.openai.api_server --trust-remote-code --enable-prefix-caching --disable-log-requests --model {ENV_MODEL_PATH} --gpu-memory-utilization 0.95 -tp {ENV_SPEC_GPU_COUNT} -pp {ENV_NODE_NUM} --port {ENV_SERVER_PORT} --served-model-name {ENV_MODEL_NAME} --max-model-len 8192", "status": "active"},
        "SGLang": {"id": "SGLang", "repository": "lmsysorg/sglang", "processor": ["nvidia"], "run_cmd": "python3 -m sglang.launch_server --model-path {ENV_MODEL_PATH} --served-model-name {ENV_MODEL_NAME} --tp {ENV_INSTANCE_GPU_COUNT} --dist-init-addr $LWS_LEADER_ADDRESS:5000 --nnodes $LWS_GROUP_SIZE --node-rank $LWS_WORKER_INDEX --trust-remote-code --host 0.0.0.0 --port {ENV_SERVER_PORT} --show-time-cost --enable-metrics --context-length 8192", "status": "active"},
        "vLLM-Ascend": {"id": "vLLM-Ascend", "repository": "vllm-ascend", "processor": ["ascend"], "run_cmd": "python3 -m vllm.entrypoints.openai.api_server --trust-remote-code --enable-prefix-caching --disable-log-requests --model {ENV_MODEL_PATH} --gpu-memory-utilization 0.95 -tp {ENV_SPEC_GPU_COUNT} -pp {ENV_NODE_NUM} --port {ENV_SERVER_PORT} --served-model-name {ENV_MODEL_NAME} --max-model-len 8192", "status": "active"},
        "MindIE": {"id": "MindIE", "repository": "mindie", "processor": ["ascend"], "status": "active"},
        # "Ollama": {"id": "Ollama", "repository": "ollama", "processor": ["cpu", "nvidia", "ascend"], "status": "active"},
        # "Llama.cpp": {"id": "Llama.cpp", "repository": "llamacpp", "processor": ["cpu", "nvidia", "ascend"], "status": "active"},
        # "transformers": {"id": "transformers", "repository": "transformers", "processor": ["cpu", "nvidia"],"status": "active"}
    }
    PREFER_VRAM_RATE = 1.2


    # Database
    DB_CONNECTION_STR: str = "postgresql+psycopg2://aicp:<EMAIL>:5432/maas"
    DB_POOL_SIZE: int = 5
    DB_ECHO: bool = False

    # Redis
    REDIS_HOST: str = "redis.pitrix.svc"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""

    # qingcloud
    QINGCLOUD_ACCESS_KEY_ID: str = ""
    QINGCLOUD_SECRET_ACCESS_KEY: str = ""
    QINGCLOUD_ZONE: str = ""
    QINGCLOUD_HOST: str = ""
    QINGCLOUD_PORT: int = 443
    QINGCLOUD_PROTOCOL: str = ""
    QINGCLOUD_CONSOLE_ID: str = ""
    QINGCLOUD_REGION: str = ""

    # job
    EVENT_QUEUE_MAX_LEN = 1000
    UNHEALTH_STATUS_THRESHOLD_TIME = 120  # 不健康持续时长(告警)
    INFER_SERVICE_UPDATE_STATUS_THRESHOLD = 10  # 1分钟状态切换次数阈值(告警)
    MONITOR_JOB_INTERVAL = 10  # 检测 job 任务间隔(秒)
    AUDIT_JOB_INTERVAL = 7200  # 审计任务间隔(秒)
    JOB_TIMEOUT = {
        'deploy_inference': 7200,
        'stop_inference': 300,
        'start_inference': 600,
        'delete_inference': 300,
        'rebuild_pod': 300,
        'increase_pod': 600,
        'decrease_pod': 600,
        'model_download':3600 * 24 * 5,
    }

    # opensearch
    OPENSEARCH_HOST: str = "https://opensearch-cluster-master.kubesphere-logging-system.svc:9200"
    OPENSEARCH_USER: str = "admin"
    OPENSEARCH_PASSWORD: str = "admin"

    # prometheus
    PROMETHEUS_SERVER = "prometheus-k8s.kubesphere-monitoring-system:9090"

    # aicp
    AICP_WEB_SERVER: str = "http://aicp-web-app.aicp-system.svc.cluster.local"
    AICP_ISTIO_SERVER: str = "http://istio-ingressgateway.istio-system.svc.cluster.local"
    MIRROR_REPOSITORY = "http://resource-proxy-service.aicp-resource:3141/root/pypi/+simple/"
    AI_CLOUD_PUSH_SERVER: str = "http://push-server-service.aicp-system:32149/push/ms"

    #operation
    OPERATION_INVOKE_EVENT_QUEUE = 'operation_invoke_event_queue'

    # proxy(nginx) 服务器域名
    @property
    def PROXY_SERVER_HOST(self) -> str:
        proxy_host = urlparse(self.INFERENCE_SERVICE_HOST)
        return f'{proxy_host.scheme}://{proxy_host.netloc}'