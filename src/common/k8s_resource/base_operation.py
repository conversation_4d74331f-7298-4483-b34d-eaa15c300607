import base64
import json
from urllib.parse import urlparse

import kr8s
import pydash
from kr8s.objects import Deployment, Namespace, Secret, Service, Job

from src.apps.billing.rsp_schema import ProductDTO
from src.apps.inference_service.req_schema import ResourceGroup
from src.apps.inference_service.rsp_schema import InfSvc, PodInfoRsp
from src.apps.model.req_schema import ModelImage
from src.common.const.comm_const import ImageType, InfSvcClusterType, PodStatus, PUBLIC_MODEL_DIR
from src.common.context import Context
from src.common.k8s_resource.costom_object import VirtualServiceObject, AuthorizationPolicyObject
from src.common.k8s_resource.k8s_helper import create_k8s_res, del_k8s_res, to_k8s_rfc_1123
from src.common.k8s_resource.spec import ResourceManager, AbsGPUStrategy
from src.setting import settings

INF_CONTAINER_NAME = "inference-container"
PUBLIC_CODE_PATH = "/maasfile/code"
PUBLIC_MAAS_MOUNT_PATH = "/public_maas"


class OperationManager:

    @staticmethod
    def get_operation(inf_svc: InfSvc):
        from src.common.k8s_resource.cluster_operation import ClusterKubeOperation
        if inf_svc.cluster_type == InfSvcClusterType.DISTRIBUTE.value:
            return ClusterKubeOperation(inf_svc)
        return KubeOperation(inf_svc)


class KubeOperation:
    def __init__(self, inf_svc: InfSvc):
        self.namespace = inf_svc.creator.lower()
        self.service_id = inf_svc.id
        self.inf_svc = inf_svc
        self.secret_name = ''
        self.image = ModelImage(**inf_svc.run_info['image'])
        self.port = self.inf_svc.run_info['port']
        self.mount = self.inf_svc.run_info['mounts'][0] if self.inf_svc.run_info['mounts'] else {}
        self.requirements = self.inf_svc.run_info['requirements']
        self.environment = self.inf_svc.run_info['environment']
        self.run_cmd = self.inf_svc.run_info['run_cmd']
        self.namespace = self.inf_svc.creator.lower()
        self.svc_name = self.service_id

    def create_namespace(self):
        create_k8s_res(Namespace, {
            "apiVersion": "v1",
            "kind": "Namespace",
            "metadata": {"name": self.namespace}
        }, ignore_err=True)

    def create_authorization_policy(self):
        template = {
            "apiVersion": "security.istio.io/v1beta1",
            "kind": "AuthorizationPolicy",
            "metadata": {
                "name": self.service_id,
                "namespace": self.namespace
            },
            "spec": {
                "action": "ALLOW",
                "rules": [{
                    "from": [{
                        "source": {"principals": ["cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account"]}
                    }, {
                        "source": {"namespaces": [self.namespace, "maas-system"]}
                    }]
                }],
                "selector": {
                    "matchLabels": {"app": self.service_id}
                }
            }
        }
        create_k8s_res(AuthorizationPolicyObject, template)

    def create_virtual_service(self):
        template = {
            "apiVersion": "networking.istio.io/v1alpha3",
            "kind": "VirtualService",
            "metadata": {
                "name": self.service_id,
                "namespace": self.namespace
            },
            "spec": {
                "selector": {"matchLabels": {"app": self.service_id}},
                "gateways": ["maas-system/inference-service-gateway"],
                "hosts": ["*"],
                "http": [{
                    "headers": {
                        "request": {
                            "set": {"maas-inference-service": self.service_id}
                        }
                    },
                    "match": [{
                        "uri": {
                            "prefix": f"/{settings.QINGCLOUD_ZONE}/inference/{self.namespace}/{self.service_id}/"
                        }
                    }],
                    "rewrite": {"uri": "/"},
                    "route": [{
                        "destination": {
                            "host": f"{self.svc_name}.{self.namespace}.svc.cluster.local"
                        }
                    }]
                }, {
                    "headers": {
                        "request": {"set": {}}
                    },
                    "match": [{
                        "uri": {
                            "prefix": f"/{settings.QINGCLOUD_ZONE}/inference-web/{self.namespace}/{self.service_id}/"
                        }
                    }],
                    "rewrite": {"uri": "/"},
                    "route": [{
                        "destination": {
                            "host": f"{self.svc_name}.{self.namespace}.svc.cluster.local",
                            "port": {"number": 8080}
                        }
                    }]
                }]
            }
        }
        create_k8s_res(VirtualServiceObject, template)

    def create_service(self):
        port_infos = [
            {"name": "inf-server", "protocol": "TCP", "port": 80, "targetPort": int(self.port)},
            {"name": "istio-metrics", "protocol": "TCP", "port": 15020, "targetPort": 15020}
        ]

        template = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": self.service_id,
                "namespace": self.namespace,
                "labels": {"maas": "inference"}
            },
            "spec": {
                "selector": {"app": self.service_id},
                "ports": port_infos
            }
        }
        create_k8s_res(Service, template)

    def create_secret(self):
        if self.image.type == ImageType.CUSTOM:
            if self.image.password:
                registry = self.image.image.split("/")[0]
                secret_name = to_k8s_rfc_1123(f"{registry.replace('.', '-').replace(':', '-')}-{self.image.username}")
                auth = {
                    registry: {
                        "username": self.image.username,
                        "password": self.image.password,
                        "auth": base64.b64encode(f"{self.image.username}:{self.image.password}".encode()).decode()
                    }
                }
            else:
                return
        else:
            secret_name = "public-docker-images-pull"
            auth = {
                settings.DOCKER_REGISTRY: {
                    "username": settings.DOCKER_ADMIN_USER,
                    "password": settings.DOCKER_ADMIN_PASSWORD,
                    "auth": base64.b64encode(f"{settings.DOCKER_ADMIN_USER}:{settings.DOCKER_ADMIN_PASSWORD}".encode()).decode()
                }
            }
        secret_template = {
            "apiVersion": "v1",
            "kind": "Secret",
            "metadata": {"name": secret_name, "namespace": self.namespace},
            "type": "kubernetes.io/dockerconfigjson",
            "data": {".dockerconfigjson": base64.b64encode(json.dumps({"auths": auth}).encode()).decode()}
        }

        if secret_template:
            del_k8s_res(Secret, secret_name, self.namespace)
            create_k8s_res(Secret, secret_template)
        self.secret_name = secret_name

    def do_create_inf_svc(self, gpu_strategy: AbsGPUStrategy, command: list, start_cmd: str, vols: list, mounts: list, env_dict_list: list):
        template =  {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": self.service_id,
                "namespace": self.namespace,
                "labels": {"maas": "inference"}
            },
            "spec": {
                "replicas": self.inf_svc.instance_num,
                "selector": {
                    "matchLabels": {"app": self.service_id}
                },
                "template": {
                    "metadata": {
                        "labels": {"app": self.service_id, "maas": "inference"}
                    },
                    "spec": {
                        "nodeSelector": gpu_strategy.node_selector,
                        "affinity": gpu_strategy.affinity,
                        "tolerations": gpu_strategy.tolerations,
                        "volumes": vols,
                        "schedulerName": gpu_strategy.scheduler,
                        "containers": [{
                            "name": INF_CONTAINER_NAME,
                            "image": self.image.image,
                            "command": command,
                            "args": [start_cmd],
                            "env": env_dict_list,
                            "resources": {'requests': gpu_strategy.requests, 'limits': gpu_strategy.limits},
                            "volumeMounts": mounts,
                            "tty": True
                        }]
                    }
                }
            }
        }
        if self.secret_name:
            template["spec"]["template"]["spec"]["imagePullSecrets"] = [{"name": self.secret_name}]
        create_k8s_res(Deployment, template)

    def create_inf_svc(self, product_info: ProductDTO, rg_info: ResourceGroup=None):
        instance_num = self.inf_svc.instance_num
        gpu_strategy = ResourceManager.create_gpu_strategy(product_info, self.inf_svc, rg_info)
        mount_path = self.mount.get('mount_path')
        host_path = self.mount.get('host_path')

        # ---------------------------------------- 环境变量 --------------------------------------
        # 平台环境变量
        env_dict = {
            'ENV_EXTERNAL_SERVER_PATH': f'/{settings.QINGCLOUD_ZONE}/inference/{self.namespace}/{self.service_id}',
            'ENV_PLAT_HOST': settings.PROXY_SERVER_HOST,
            'ENV_MODEL_ID': self.inf_svc.model_id,
            'ENV_MODEL_NAME': self.inf_svc.model.split('/')[1],
            'ENV_MODEL_PATH': mount_path,
            'ENV_SPEC_GPU_COUNT': product_info.gpu_count,
            'ENV_SPEC_GPU_NAME': product_info.gpu_model,
            'ENV_SPEC_GPU_MEMORY': product_info.gpu_memory,
            'ENV_SPEC_CPU': product_info.cpu_count,
            'ENV_SPEC_MEMORY': product_info.memory,
            'ENV_INSTANCE_GPU_COUNT': product_info.gpu_count * self.inf_svc.node_num,  # 单个实例总 gpu 数量
            'ENV_TOTAL_GPU_COUNT': product_info.gpu_count * instance_num * self.inf_svc.node_num,  # 总 gpu 数量
            'ENV_SERVER_PORT': self.port,
            'ENV_INSTANCE_NUM': self.inf_svc.instance_num,  # 实例个数
            'ENV_NODE_NUM': self.inf_svc.node_num,  # 每个实例节点个数
        }
        # 公共环境变量
        env_dict.update({
            'AICP_PLATFORM': settings.AICP_PLATFORM,
            'AICP_TYPE': 'INFER',
            'AICP_USER_NAME': Context.USER.get().user_name,
            'AICP_NAME': self.inf_svc.name,
        })

        # 兼容单节点部署，增加环境变量
        if self.inf_svc.cluster_type == InfSvcClusterType.SINGLE.value:
            env_dict.update({
                'LWS_LEADER_ADDRESS': '127.0.0.1',
                'LWS_GROUP_SIZE': 1,
                'LWS_WORKER_INDEX': 0,
            })

        # 用户环境变量
        if self.environment:
            env_list = self.environment.split(',')
            env_list = [env.split('=') for env in env_list]
            env_dict.update({env[0]: env[1] for env in env_list})

        # 替换命令中的占位符
        for key, val in env_dict.items():
            place_holder = f'{{{key}}}'
            if place_holder in self.run_cmd:
                self.run_cmd = self.run_cmd.replace(place_holder, str(val))

        env_dict_list = [{'name': key, 'value': str(val)} for key, val in env_dict.items()]
        env_dict_list.append({"name": "AICP_HOST_MACHINE", "valueFrom": {"fieldRef": {"fieldPath": "spec.nodeName"}}})

        # 资源环境变量
        env_dict_list.extend(gpu_strategy.ext_env)

        # ---------------------------------------- 服务依赖 --------------------------------------
        start_cmd = ''
        if self.requirements:
            if not self.requirements.startswith('pip install'):
                # requirements.txt 文件方式
                if self.requirements.startswith('/'):
                    requirements_file = self.requirements.split(mount_path)[-1]
                    if not requirements_file.startswith('/') and not mount_path.endswith('/'):
                        requirements_file = '/' + requirements_file
                    self.requirements = f'pip install -r {mount_path}{requirements_file}'
                else:
                    self.requirements = 'pip install %s' % self.requirements
            if settings.MIRROR_REPOSITORY:
                self.requirements = self.requirements + f' -i {settings.MIRROR_REPOSITORY} '
                parsed_url = urlparse(settings.MIRROR_REPOSITORY)
                if parsed_url.scheme == 'http':
                    url = parsed_url.netloc
                    domain = url.split(':')[0]
                    self.requirements += f' --trusted-host {domain}'

        # ---------------------------------------- 启动命令 --------------------------------------
        command = []
        if self.run_cmd:
            command = ['/bin/bash', '-i', '-c']
            start_cmd = f'{self.requirements} && {self.run_cmd}' if self.requirements else self.run_cmd

        # ---------------------------------------- 硬盘挂载 --------------------------------------
        # 挂载模型文件目录
        vols = [{'name': 'model-volume', 'hostPath': {'path': settings.GPFS_ROOT_DIR + host_path}}]
        mounts = [{'mountPath': mount_path, 'name': 'model-volume', 'readOnly': True}]

        # 挂载公共代码目录
        if self.inf_svc.creator in settings.SUPER_ACCOUNTS:
            vols.append({"name": "public-code-volume", "hostPath": {"path": settings.GPFS_ROOT_DIR + PUBLIC_CODE_PATH}})
            mounts.append({"mountPath": PUBLIC_MAAS_MOUNT_PATH, "name": "public-code-volume", "readOnly": True})

        # 挂载 gpu 扩展目录
        vols.extend(gpu_strategy.vol)
        mounts.extend(gpu_strategy.mount)

        # ---------------------------------------- 组装模板 --------------------------------------
        self.do_create_inf_svc(gpu_strategy, command, start_cmd, vols, mounts, env_dict_list)

    def scale_inf_svc(self, target_replicas: int):
        """
        推理服务扩缩容
        :param target_replicas: 目标副本
        :return:
        """
        deploy = Deployment.get(self.service_id, namespace=self.namespace)  # noqa
        deploy.scale(target_replicas)

    def delete(self):
        Service.get(self.service_id, namespace=self.namespace).delete()  # noqa
        AuthorizationPolicyObject.get(self.service_id, namespace=self.namespace).delete()
        VirtualServiceObject.get(self.service_id, namespace=self.namespace).delete()
        Deployment.get(self.service_id, namespace=self.namespace).delete()  # noqa

    def get_svc_pods(self, status: str = '', sort_key: str = '', reverse: int = 0):
        service_id = self.service_id
        pods = kr8s.get('pod', namespace=self.inf_svc.creator.lower(), label_selector=f'app={service_id}')
        pod_info = []
        running_pod_num = 0
        for pod in pods:  # noqa

            if pod.metadata.get('deletionTimestamp'):
                continue

            containers = pod.status.get("containerStatuses", [])
            inf_container = pydash.find(containers, lambda c: c.name == INF_CONTAINER_NAME)

            if not inf_container or not inf_container.ready:
                pod_status = self.inf_svc.transition_status or PodStatus.FAILED.value
            else:
                pod_status = PodStatus.ACTIVE.value
                running_pod_num += 1
            restart_count = inf_container.restartCount if inf_container else 0

            if status and status != pod_status:
                continue

            pod_info.append({
                "pod_id": pod.name,
                "restart_count": restart_count,
                "create_time": pod.status.get('startTime', ''),
                "pod_status": pod_status
            })
        if sort_key:
            pod_info = sorted(pod_info, key=lambda k: k[sort_key], reverse=reverse)  # noqa

        return PodInfoRsp(**{
            "running_pod_num": running_pod_num,
            "pod_num": self.inf_svc.instance_num * self.inf_svc.node_num,
            "pods": pod_info
        })

def create_download_model_job(source_channel, model_id, source_model, image, job_id):
    template = {
        "apiVersion": "batch/v1",
        "kind": "Job",
        "metadata": {
            "name": model_id,
            "namespace": "maas-system",
            "labels": {
                "app": "model-download",
            },
            "annotations": {
                "sidecar.istio.io/inject": "false",
            }
        },
        "spec": {
            "template": {
                "metadata": {
                    "annotations": {
                        "sidecar.istio.io/inject": "false",
                    }
                },
                "spec": {
                    "containers": [{
                        "name": "model-download",
                        "image": image,
                        "imagePullPolicy": "Always",
                        "command": ["python3"],
                        "args": ["main.py", "--source_channel", source_channel, "--model_name", source_model,
                                 "--redis_host", settings.REDIS_HOST, "--redis_port", str(settings.REDIS_PORT),
                                 "--job_id", job_id, "--model_id", model_id
                                 ],
                        "volumeMounts": [{
                            "name": "public-model",
                            "mountPath": "/public_model"
                        }],
                        "resources": {
                            "limits": {"cpu": "1", "memory": "1Gi"},
                            "requests": {"cpu": "1", "memory": "1Gi"}
                        }
                    }],
                    "volumes": [{
                        "name": "public-model",
                        "hostPath": {
                            "path": settings.GPFS_ROOT_DIR + PUBLIC_MODEL_DIR,
                            "type": "DirectoryOrCreate",
                        }
                    }],
                    "restartPolicy": "Never",
                }
            },
            "backoffLimit": 2,
            "ttlSecondsAfterFinished": 3600,
            "activeDeadlineSeconds": settings.JOB_TIMEOUT['model_download']
        }
    }
    create_k8s_res(Job, template)