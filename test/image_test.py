# -*- coding: utf-8 -*-
import os
import unittest

from src.common.const.comm_const import ImageType

os.environ['ENV_CONF'] = 'dev'
from src.apps.image.curd import image_curd


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self) -> None:
        ...

    def test_list(self):  # noqa
        images = image_curd.list(ImageType.OFFICIAL)
        print(images)


if __name__ == '__main__':
    unittest.main()
