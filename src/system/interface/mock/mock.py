# -*- coding: utf-8 -*-
from src.common.dto import User, AccessKey
from src.system.interface.abs_billing_interface import AbsBillingInterface
from src.system.interface.abs_product_interface import AbsProductInterface
from src.system.interface.abs_user_interface import AbsUserInterface


class MockUser(AbsUserInterface):

    def get_access_key(self, user_id: str) -> AccessKey:
        return AccessKey(access_key_id="", secret_access_key="")

    def get_user_by_id(self, user_id: str) -> User:
        return User("usr-12345", "user", "测试用户")

class MockBilling(AbsBillingInterface):

    def get_charge_records(self, resource_id: str, user_id: str, offset: int = 0, limit: int = 20):
        # {'charge_record_set': [{'resource_type': 'qai', 'fee': '44.48', 'user_id': 'usr-mj3o0PTr', 'zone_id': 'staging', 'resource_id': 'qai-inf-88888888', 'start_time': '2024-04-25T09:49:59Z', 'tags': [], 'charge_item': '', 'console_id': 'admin', 'currency': 'cny', 'root_user_id': 'usr-mj3o0PTr', 'resource_name': '', 'remarks': 'resource has been changed, charge fee', 'end_time': '2024-04-25T11:49:59Z', 'duration': '1h', 'charge_time': '2024-04-25T09:49:59Z', 'total_sum': 0, 'discount': 100, 'price': '22.24', 'unit': 'hour', 'contract_id': 'ct-IUjym5Se'}, {'resource_type': 'qai', 'fee': '0.4355', 'user_id': 'usr-mj3o0PTr', 'zone_id': 'staging', 'resource_id': 'qai-inf-88888888', 'start_time': '2024-04-25T09:47:38Z', 'tags': [], 'charge_item': '', 'console_id': 'admin', 'currency': 'cny', 'root_user_id': 'usr-mj3o0PTr', 'resource_name': '', 'remarks': 'resource has been created, charge fee', 'end_time': '2024-04-25T09:49:59Z', 'duration': '1h', 'charge_time': '2024-04-25T09:49:07Z', 'total_sum': 0, 'discount': 100, 'price': '11.12', 'unit': 'hour', 'contract_id': 'ct-cZspIALC'}], 'ret_code': 0, 'total_count': 2, 'total_sum': '44.9155', 'trace_id': 'igPsTjr2', 'action': 'GetChargeRecordsResponse'}
        return {'charge_record_set': [], 'ret_code': 0, 'total_count': 0, 'total_sum': '0', 'action': 'GetChargeRecordsResponse'}

    def get_price(self, user_id: str, price_info: dict, duration: int = 3600, charge_mode: str = "elastic"):
        # {'action': 'GetPriceResponse', 'price_set': [{'original_price': '33.36', 'available_coupon': '0', 'sequence': 0, 'price': '33.36', 'normal_price': '33.36', 'discount_details': {}, 'discount': 100, 'available_coupon_set': []}], 'trace_id': 'LfoPR1Z3', 'ret_code': 0}
        return {'action': 'GetPriceResponse', 'price_set': [{'original_price': '0', 'available_coupon': '0', 'sequence': 0, 'price': '0', 'normal_price': '0', 'discount_details': {}, 'discount': 100, 'available_coupon_set': []}], 'ret_code': 0}

    def lease(self, resource_id: str, user_id: str, price_info: dict, duration: int = 3600,
              charge_mode: str = "elastic", **kwargs):
        # return {'message': 'LeaseResourceFailed, lease [qai-inf-12345678] failed', 'trace_id': 'CjOIJu5p', 'ret_code': 2200}
        return {'action': 'LeaseResponse', 'resource_set': [{'resource_id': resource_id}], 'ret_code': 0}

    def check_balance(self, resource_id: str, user_id: str, price_info: dict, price_type: str = "new"):
        # return {'action': 'CheckResourcesBalance', 'message': 'NotEnoughMoney, The need to total cost [33.36], coupons deduction:[0],
        # [33.36] need cash, your available balance [-1196.3891], not enough to support the operation, please recharge'}
        return {'action': 'CheckResourcesBalanceResponse', 'ret_code': 0}

    def unlease(self, resource_id: str, user_id: str):
        return {'action': 'UnleaseResponse', 'resource_set': [{'resource_id': resource_id}], 'ret_code': 0}

    def get_lease_info(self, resource_id: str, user_id: str):
        # 没有计费信息
        # return {'message': 'ResourceNotFound, resource [qai-inf-12345678] lease info not found', 'trace_id': 'Qehd5Tv3', 'ret_code': 2100}

        # 有计费信息
        # return {'action': 'GetLeaseInfoResponse', 'resource_id': 'qai-inf-12345678', 'ret_code': 0, 'trace_id': 'JU7wGDTm',
        #         'lease_info': {'status': 'creating', 'lease_time': None, 'zone_id': 'staging', 'resource_id': 'qai-inf-12345678', 'renewal': 'auto',
        #                        'contract': {'group_id': '0', 'contract_id': 'ct-2q8zjCQb', 'resource_id': 'qai-inf-12345678', 'charge_mode': 'elastic', 'start_time': None, 'console_id': 'admin', 'next_charge_mode': None, 'zone_id': 'staging', 'create_time': '2024-04-17T05:37:38Z', 'end_time': None,
        #                                     'price_info': {'status': 'sale', 'aipods_usage': 'high_inferencd', 'sku_id': 'sku_OBkDDJJwYvgM', 'zone': 'gamma', 'replicas': 3, 'aipods_type': 'only_gpu', 'cpu_count': 8, 'cpu_model': 'intel', 'os_disk': 50, 'aipods_scope': 'inference_compute', 'memory': 16, 'gpu_model': 'NVIDIA 4090', 'disk': 50, 'spec_id': 'aipods', 'gpu_memory': 24, 'gpu_count': 2},
        #                                     'discount': 100, 'long_resource_id': None, 'product_id': None, 'price': '33.36', 'duration': '1h', 'auto_renew': 0},
        #                        'console_id': 'admin', 'status_time': '2024-04-17T05:37:38Z',
        #                        'all_contracts': [{'contract_id': 'ct-2q8zjCQb', 'start_time': None, 'discount': 100, 'create_time': '2024-04-17T05:37:38Z', 'end_time': None, 'duration': 3600, 'price': 333600, 'product_id': None}],
        #                        'root_user_id': 'usr-UYbNAKr3', 'products': [], 'unlease_time': None, 'renewal_time': None, 'user_id': 'usr-UYbNAKr3', 'group_id': '0'}}

        return {'action': 'GetLeaseInfoResponse', 'resource_id': resource_id, 'ret_code': 0, 'create_time': '',
                'lease_info': {'status': 'active', 'status_time': '', 'renewal': 'auto', 'contract': {'discount': 100, 'price': '0', 'duration': '1h', 'auto_renew': 0, 'charge_mode': 'elastic' }}}


class MockProduct(AbsProductInterface):

    def get_resource_id(self, product_id) -> str:
        return product_id

    def get_product_by_id(self, product_id: str):
        return {'ret_code': 0, 'trace_id': 'LJIjD75y', 'action': 'ProductCenterQueryRequestResponse', 'total': '1', 'spec': None,
         'skus': [{'status': 'sale', 'update_time': '2024-04-10T06:36:16.867963Z', 'sku_id': 'sku_OBkDDJJwYvgM', 'month_discounts': [], 'quote': False, 'modifiable': 1, 'sku_code': 'ddb0ac9f03983396b9664b8116ba9b87', 'prod_id': 'prod_z21v55gV7WlP', 'console_id': 'admin', 'own': 1, 'create_time': '2024-04-10T06:36:16.867963Z', 'sku_label_sets': [],
         'filters': [{'name': '单卡', 'operator': '==', 'attr_id': 'aipods_type', 'attr_value': 'only_gpu', 'usage': 2, 'value_type': 'string'},
         {'name': '推理服务', 'operator': '==', 'attr_id': 'aipods_scope', 'attr_value': 'inference_compute', 'usage': 2, 'value_type': 'string'},
         {'name': '高速推理', 'operator': '==', 'attr_id': 'aipods_usage', 'attr_value': 'high_inferencd', 'usage': 2, 'value_type': 'string'},
         {'name': '8核', 'operator': '==', 'attr_id': 'cpu_count', 'attr_value': '8', 'usage': 3, 'value_type': 'number'},
         {'name': '16G', 'operator': '==', 'attr_id': 'memory', 'attr_value': '16', 'usage': 3, 'value_type': 'number'},
         {'name': 'intel', 'operator': '==', 'attr_id': 'cpu_model', 'attr_value': 'intel', 'usage': 3, 'value_type': 'string'},
         {'name': 'NVIDIA 4090', 'operator': '==', 'attr_id': 'gpu_model', 'attr_value': 'NVIDIA 4090', 'usage': 3, 'value_type': 'string'},
         {'name': '2', 'operator': '==', 'attr_id': 'gpu_count', 'attr_value': '2', 'usage': 3, 'value_type': 'number'},
         {'name': '24G', 'operator': '==', 'attr_id': 'gpu_memory', 'attr_value': '24', 'usage': 3, 'value_type': 'number'},
         {'name': '50GB', 'operator': '==', 'attr_id': 'os_disk', 'attr_value': '50', 'usage': 2, 'value_type': 'number'},
         {'name': '50GB', 'operator': '==', 'attr_id': 'disk', 'attr_value': '50', 'usage': 2, 'value_type': 'number'}],
         'prices': [{'price_type': 'month', 'currency': 'cny', 'price': 109, 'priority': 0}, {'price_type': 'hour', 'currency': 'cny', 'price': 11.12, 'priority': 0}],
         'attr_id': 'replicas', 'extra_info': '', 'spec_id': 'spec_NJ28EYY4OXKk', 'place_times': -1, 'region_id': 'staging'}]}


mock_user = MockUser()
mock_billing = MockBilling()
mock_product = MockProduct()
