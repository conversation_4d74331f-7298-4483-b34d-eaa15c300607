# -*- coding: utf-8 -*-

from src.apps.base_curd import BaseCURD
from src.setting import settings
from src.system.integrations.aicpserver import aicp_client


TAG_MODULE = "maas"

class TagCURD(BaseCURD):

    @staticmethod
    def save_resource_tag(model_id: str, tag_ids: list[str]):
        data = {"module": TAG_MODULE, "resource_id": model_id, "tag_ids": tag_ids, "region": settings.QINGCLOUD_REGION}
        aicp_client.send_request(settings.PROXY_SERVER_HOST, '/global/product/api/tag/resource-tag',
                                 auth_type=aicp_client.AuthType.SIGNATURE, method="POST", json=data)

    @staticmethod
    def get_tag_by_resource(model_ids: list[str]) -> dict[str, list[dict]]:
        params = {"module": TAG_MODULE, "resource_ids": model_ids, "region": settings.QINGCLOUD_REGION}
        data = aicp_client.send_request(settings.PROXY_SERVER_HOST, '/global/product/api/tag/resource-tag',
                                        auth_type=aicp_client.AuthType.SIGNATURE, method="GET", params=params)
        return data['data']


    @staticmethod
    def get_resource_by_tag(tag_ids: list[str]) -> list[dict]:
        params = {"module": TAG_MODULE, "tag_ids": tag_ids, "region": settings.QINGCLOUD_REGION}
        data = aicp_client.send_request(settings.PROXY_SERVER_HOST, '/global/product/api/tag/tag-resource',
                                        auth_type=aicp_client.AuthType.SIGNATURE, method="GET", params=params)
        return data['list']
tag_curd = TagCURD()
