apiVersion: apps/v1
kind: Deployment
metadata:
  name: model-manage-server
  labels:
    app: model-manage-server
    version: production
spec:
  selector:
    matchLabels:
      app: model-manage-server
      version: production
  replicas: 1
  template:
    metadata:
      labels:
        app: model-manage-server
        version: production
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      containers:
        - name: model-manage-server
          image: hub.kubesphere.com.cn/aicp/model-manage-server:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 5001
          env:
            - name: TZ
              value: Asia/Shanghai
          volumeMounts:
            - name: maas-file
              mountPath: /maasfile
            - name: config-file
              mountPath: /code/config
      volumes:
        - name: maas-file
          hostPath:
            path: /share/maasfile
            type: DirectoryOrCreate
        - name: config-file
          configMap:
            name: model-manage-server-config
      serviceAccountName: model-manage-service-account
