# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from pydantic import computed_field
from sqlalchemy import String
from sqlalchemy.dialects.postgresql import ARRAY, JSON
from sqlmodel import SQLModel, Field

from src.apps.tag.rsp_schema import ModelTag
from src.common.const.comm_const import ModelStatus, DEPLOY_CUSTOM
from src.setting import settings
from src.system.interface import PI


class ModelInfEngine(SQLModel, table=True):
    __tablename__ = "model_inf_engine"
    model_id: str = Field(primary_key=True)
    engine_id: str = Field(primary_key=True)
    run_cmd: Optional[str]


class ModelBase(SQLModel):
    """
    个人列表页能看到的字段
    """
    id: str = Field(primary_key=True)
    name: str
    icon: Optional[str] = None
    status: str = Field(default=ModelStatus.DRAFT.value)
    brief: str
    developer: str
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    creator: str
    size: Optional[int] = Field(default=0)
    provider: str
    tags: Optional[list[str]] = Field(default=[], sa_type=ARRAY(String))  # noqa
    n_params: Optional[float] = 0

    @computed_field
    @property
    def model_tags(self) -> list[ModelTag]:
        return getattr(self, '_model_tags', [])

    @model_tags.setter
    def model_tags(self, value):
        self._model_tags = value  # noqa

    @computed_field
    @property
    def download_progress(self) -> float:
        return getattr(self, '_download_progress', 0)

    @download_progress.setter
    def download_progress(self, value):
        self._download_progress = value  # noqa


class ModelDetail(ModelBase):
    """
    个人详情页能看到的字段
    """
    requirements: Optional[str]
    port: int
    environment: Optional[str]
    source_channel: Optional[str]
    mounts: Optional[list[dict]] = Field(default=None, sa_type=JSON)
    image: Optional[dict] = Field(default=None, sa_type=JSON)
    ext: Optional[dict] = Field(default=None, sa_type=JSON)
    deploy_editable: int

    @computed_field
    def prefer_vram(self) -> int:
        return int(self.size * settings.PREFER_VRAM_RATE) if self.size else 0

    @computed_field
    def creator_name(self) -> str:
        return PI.user_interface.get_user_by_id(self.creator).user_name

    @computed_field
    @property
    def inference_engine(self) -> list[ModelInfEngine]:
        return getattr(self, '_inference_engine', [])

    @inference_engine.setter
    def inference_engine(self, value):
        self._inference_engine = value  # noqa

    @computed_field
    def deploy_type(self) -> list[str]:
        types = [i.engine_id for i in self.inference_engine]
        if self.image:
            types.append(DEPLOY_CUSTOM)
        return types


class Model(ModelDetail, table=True):
    __tablename__ = "model"
