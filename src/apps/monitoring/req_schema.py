# -*- coding: utf-8 -*-
from typing import Optional, List

from fastapi import Query


class InferServiceMeterQuerySchema:

    def __init__(self,
                 start_time: Optional[int] = None,
                 end_time: Optional[int] = None,
                 metrics: List[str] = Query(default=["pod_memory_usage_wo_cache", "pod_cpu_usage", "qps",
                                                     "pod_net_bytes_received", "http_response_total", "pod_gpu_util",
                                                     "pod_gpu_mem_usage", "pod_net_bytes_transmitted"],
                                            description="过滤指标, 支持 'pod_cpu_usage', 'pod_memory_usage', "
                                                        "'pod_memory_usage_wo_cache', 'pod_net_bytes_transmitted', "
                                                        "'pod_net_bytes_received', 'pod_gpu_util', 'pod_gpu_mem_usage',"
                                                        "'pod_tensor_core_util'"),
                 step: int = Query(default=None, description="采样时间间隔")
                 ):
        self.start_time = start_time
        self.end_time = end_time
        self.metrics = metrics
        self.step = None
        if step:
            self.step = step
        self.namespace = ""
        self.pod_filter = []
        self.full_pod_filter = []
        self.service_filter = []
