# -*- coding: utf-8 -*-

import os
import unittest

os.environ['ENV_CONF'] = 'dev'
from src.system.interface import PI


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self) -> None:
        self.resource_id = "inf-0g22v2ee"
        self.user_id = "usr-mj3o0PTr"  # staging <EMAIL>
        # self.user_id = "usr-mj3o0PTr"  # 后付费
        self.sku_id = "sku_4wr740gWEGvW"
        self.qincloud_user = "usr-ohn3PIoq"
        self.qincloud_sku_id = "sku_vmmWQJEZWEwX"
        self.price_info = {
            "replicas": 1,
            "sku_id": self.sku_id,
            "status": 'sale',
            "aipods_type": 'only_gpu',
            "aipods_scope": 'inference_compute',
            "aipods_usage": 'high_inferencd',
            "cpu_count": 8,
            "memory" : 16,
            "cpu_model": 'intel',
            "gpu_model": 'NVIDIA 4090',
            "gpu_count": 2,
            "gpu_memory" : 24,
            "os_disk": 50,
            "disk": 50,
        }

    async def test_get_lease_info(self):
        ret = PI.billing_interface.get_lease_info(self.resource_id, self.user_id)
        print(ret)

    async def test_lease(self):
        # self.price_info["replicas"] = 2
        ret = PI.billing_interface.lease(self.resource_id, self.user_id, self.price_info)
        print(ret)

    async def test_get_price(self):
        ret = PI.billing_interface.get_price(self.user_id, self.price_info)
        print(ret)

    async def test_unlease(self):
        ret = PI.billing_interface.unlease(self.resource_id, self.user_id)
        print(ret)

    async def test_checkbalance(self):
        price_info = PI.product_interface.get_product_by_id(self.sku_id).dict()
        price_info["replicas"] = 1
        ret = PI.billing_interface.check_balance(self.resource_id, self.user_id, price_info)
        print(ret)

    async def test_get_charge_records(self):
        ret = PI.billing_interface.get_charge_records("qai-inf-r7zal72y", "admin")
        print(ret)


if __name__ == '__main__':
    unittest.main()

