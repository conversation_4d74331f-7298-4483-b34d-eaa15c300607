# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod

from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException


class AbsBillingInterface(ABC):
    """
    计费相关接口
    """

    @abstractmethod
    def get_price(self, user_id: str, price_info: dict, duration: int = 3600, charge_mode: str = "elastic"):
        """
        查询配置价格
        :param user_id: 用户id
        :param price_info: 价格配置
        :param duration: 周期
        :param charge_mode: 付费方式
        :return: 价格信息
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def lease(self, resource_id: str, user_id: str, price_info: dict,
              duration: int = 3600, charge_mode: str = "elastic", **kwargs):
        """
        创建或更新租赁信息
        :param resource_id: 资源 id
        :param user_id: 用户 id
        :param price_info: 资源信息
        :param duration: 计费周期（秒）
        :param charge_mode: 计费方式（按需/包月）
        :param kwargs:
        :return:
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def check_balance(self, resource_id: str, user_id: str, price_info: dict, price_type: str = "new"):
        """
        检查余额
        :param resource_id: 资源id
        :param user_id: 用户id
        :param price_info: 资源信息
        :param price_type: 价格类型 new/renewal/resize/resume/recover/instant/modify_next_charge_mode
        :return:
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def unlease(self, resource_id: str, user_id: str):
        """
        停止计费
        :param resource_id: 资源id
        :param user_id: 用户id
        :return:
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def get_lease_info(self, resource_id: str, user_id: str):
        """
        查询租赁信息
        :param resource_id: 资源id
        :param user_id: 用户 id
        :return:
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def get_charge_records(self, resource_id: str, user_id: str, offset: int = 0, limit: int = 20):
        """
        获取消费记录
        :param resource_id: 资源id
        :param user_id: 用户id
        :param offset: 分页偏移
        :param limit: 数量
        :return: 消费记录
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

