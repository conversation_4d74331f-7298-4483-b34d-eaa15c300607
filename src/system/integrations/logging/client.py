# -*- coding: utf-8 -*-
from datetime import datetime, timezone

from opensearchpy import OpenSearch

from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException
from src.setting import settings
from src.common.utils.data import  nano_to_datetime, datetime_to_nano

from src.common.loggers import logger



class OpenSearchClient:

    def __init__(self):
        self.log_index_name = "host-logs*"
        self.kube_resource_index_name = "kube_resource"
        self._client = OpenSearch(
            hosts=[settings.OPENSEARCH_HOST],
            http_auth=(settings.OPENSEARCH_USER, settings.OPENSEARCH_PASSWORD),
            verify_certs=False,
            ssl_show_warn=False
        )

    def get_all_pod(self, service_id: str, namespace: str):
        musts = [
            {
                "term": {
                    "kind": "Pod"
                }
            },
            {
                "term": {
                    "metadata.namespace": namespace
                }
            },
            {
                "term": {
                    "metadata.labels.app": service_id
                }
            }
        ]
        body = {
            "query": {
                "bool": {
                    "must": musts
                }
            },
            "_source": ["metadata.name"],
            "size": 10000,
            "from": 0
        }
        logs = self._client.search(index=self.kube_resource_index_name, body=body)
        return [x["_source"] for x in logs["hits"]["hits"]]

    def search_pod_log(self, pod_id: list, key_word: str = None, start_time: datetime = None, end_time: datetime = None,
                      size: int = 10000, page: int = 0, reverse: bool = False, stream: bool = False):
        """
        查询pod日志
        :param pod_id: pod ID列表
        :param key_word: 关键词
        :param start_time: 开始时间
        :param end_time: 结束时间
        :param size: 每页大小
        :param page: 页码
        :param reverse: 是否倒序
        :param stream: 是否以流式格式返回
        :return: (logs, total) 元组，logs 为日志列表或格式化的日志字符串
        """
        try:
            musts = [
                {
                    "terms": {
                        "kubernetes.pod_name.keyword": pod_id
                    }
                },
                {
                    "terms": {
                        "kubernetes.container_name.keyword": ["gpfs-container", "inference-container"]
                    }
                }
            ]

            # 添加时间范围查询
            if start_time:
                musts.append({
                    "range": {
                        "unixtime": {
                            "gte": datetime_to_nano(start_time)
                        }
                    }
                })
            if end_time:
                musts.append({
                    "range": {
                        "unixtime": {
                            "lte": datetime_to_nano(end_time)
                        }
                    }
                })

            # 添加关键词查询
            if key_word:
                musts.append({
                    "match_phrase": {
                        "log": key_word
                    }
                })

            # 构建查询请求
            search_body = {
                "query": {
                    "bool": {
                        "must": musts
                    }
                },
                "_source": ["log", "timestamp", "unixtime"],
                "sort": [
                    {
                        "unixtime": {
                            "order": "desc" if reverse else "asc"
                        }
                    }
                ],
                "size": size,
                "from": page * size
            }

            # 添加关键词高亮
            if key_word and not stream:
                search_body["highlight"] = {
                    "pre_tags": ["<highlight>"],
                    "post_tags": ["</highlight>"],
                    "fields": {
                        "log": {
                            "type": "plain",
                            "fragmenter": "span",
                            "number_of_fragments": 0
                        }
                    }
                }

            logger.debug(f"开始查询日志，参数：pod_id={pod_id}, key_word={key_word}, start_time={start_time}, end_time={end_time}")
            response = self._client.search(
                index=self.log_index_name,
                body=search_body
            )

            hits = response["hits"]["hits"]
            if not hits:
                logger.debug("未找到相关日志")
                if stream:
                    return "未找到相关日志\n", 0
                return [], 0

            if stream:
                logs = []
                for hit in hits:
                    source = hit["_source"]
                    if "unixtime" in source:
                        dt = nano_to_datetime(source["unixtime"])
                        source["timestamp"] = dt.astimezone(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
                    logs.append(f"[{source['timestamp']}] {source['log']}\n")
                logger.debug(f"查询到 {len(logs)} 条日志")
                return "".join(logs), len(logs)
            else:
                results = []
                for hit in hits:
                    source = hit["_source"]
                    if "highlight" in hit and "log" in hit["highlight"]:
                        source["log"] = hit["highlight"]["log"][0]
                    results.append(source)
                logger.debug(f"查询到 {len(results)} 条日志")
                return results, response["hits"]["total"]["value"]

        except Exception as e:
            logger.error(f"OpenSearch 客户端操作失败: {str(e)}")
            raise MaaSBaseException(Err.SERVER_ERR)



opensearch_client = OpenSearchClient()
