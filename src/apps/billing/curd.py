# -*- coding: utf-8 -*-
from typing import List

from src.apps.base_curd import BaseCURD
from src.apps.billing.req_schema import GetChargeReq
from src.apps.billing.rsp_schema import LeaseInfo, Price, ChargeData
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.exceptions import MaaSBaseException
from src.setting import settings
from src.system.interface import PI

DEF_RES_ID = "inf-00000000"


class BillingCURD(BaseCURD):

    @staticmethod
    async def valid_balance(sku_id: str, replica: int, resource_id: str = DEF_RES_ID, user_id: str = None):
        """
        校验用户余额
        :param sku_id: 购买产品 id
        :param replica: 目标副本数
        :param resource_id: 资源id（创建、启动不传，扩缩容需要传）
        :param user_id: 实际扣费账户（管理员触发场景）
        :return: 余额不足自动抛异常
        """

        price_info = PI.product_interface.get_product_by_id(sku_id).dict()
        price_info.update({"replicas": replica, "sku_id": sku_id})
        price_type = "new" if not resource_id or resource_id == DEF_RES_ID else "resize"
        PI.billing_interface.check_balance(resource_id, user_id or Context.USER.get().owner, price_info, price_type)

    @staticmethod
    async def lease(resource_id: str, sku_id: str, replica: int, user_id: str = None):
        """
        资源创建计费或更新计费
        :param resource_id: 资源id
        :param sku_id: 产品id
        :param replica: 目标计费副本数
        :param user_id: 实际扣费账户（管理员触发场景）
        :return: 失败自动抛异常
        """

        price_info = PI.product_interface.get_product_by_id(sku_id).dict()
        price_info.update({"replicas": replica, "sku_id": sku_id})
        PI.billing_interface.lease(resource_id, user_id or Context.USER.get().owner, price_info)

    @staticmethod
    async def unlease(resource_id: str, user_id: str = None):
        """
        释放租赁信息，资源删除时调用
        :param resource_id: 资源id
        :param user_id: 实际扣费账户（管理员触发场景）
        :return: 失败自动抛异常
        """

        PI.billing_interface.unlease(resource_id, user_id or Context.USER.get().owner)

    @staticmethod
    async def get_lease_info(resource_id: str, user_id: str = None) -> LeaseInfo:
        """
        查询资源的租赁信息
        :param resource_id: 资源id
        :param user_id: 实际扣费账户（管理员触发场景）
        :return: 租赁信息
        """
        lease_info = PI.billing_interface.get_lease_info(resource_id, user_id or Context.USER.get().owner)["lease_info"]
        return LeaseInfo(**lease_info)

    @staticmethod
    async def get_price(sku_id: str, replica: int, duration: int = 3600, charge_mode: str = "elastic") -> List[Price]:
        """
        查询资源价格
        :param sku_id: 资源规格 id
        :param replica: 副本数量
        :param duration: 计费周期
        :param charge_mode:计费模式（按需、包月）
        :return:
        """
        price_info = PI.product_interface.get_product_by_id(sku_id).dict()
        price_info.update({"replicas": replica, "sku_id": sku_id})
        ret = PI.billing_interface.get_price(Context.USER.get().owner, price_info, duration, charge_mode)
        return [Price(**item) for item in ret["price_set"]]

    @staticmethod
    async def get_charge_records(req: GetChargeReq) -> ChargeData:
        """
        查询资源扣费信息
        :param req: 查询参数：资源id、用户、分页信息
        :return: 扣费信息
        """
        charge_ret = PI.billing_interface.get_charge_records(req.resource_id, req.user_id or Context.USER.get().owner,
                                                             req.offset, req.size)
        return ChargeData(**charge_ret)

    @staticmethod
    async def valid_lease_status(resource_id: str, user_id: str, expect_status: list[str] = None):
        """
        校验租赁状态
        :param resource_id: 资源id
        :param user_id: 资源所有者
        :param expect_status: 期望状态
        """
        if not settings.BILLING_ENABLE:
            return
        expect_status = expect_status or ['active']
        lease_info = await BillingCURD.get_lease_info(resource_id, user_id)
        if lease_info.status not in expect_status:
            raise MaaSBaseException(Err.ILLEGAL_LEASE_STATUS, resource_id=resource_id, lease_status=lease_info.status)

