apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: model-manage-server
spec:
  gateways:
    - maas-gateway
  hosts:
    - "*"
  http:
    - headers:
        request:
          add:
            x-forwarded-prefix: /maas
      match:
        - uri:
            prefix: /qai/maas
        - uri:
            prefix: /maas
        - uri:
            prefix: /kapis/aicp.kubesphere.io/v1/maas
      rewrite:
        uri: /maas
      route:
        - destination:
            host: model-manage-server
            subset: production
      name: model-manage-server
