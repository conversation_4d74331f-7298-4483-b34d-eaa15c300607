# -*- coding: utf-8 -*-
import requests

from src.common.loggers import logger
from src.setting import settings


class PromethusClient:

    def __init__(self):
        self.url = f'http://{settings.PROMETHEUS_SERVER}/api/v1/query'
        self.rang_url = f'http://{settings.PROMETHEUS_SERVER}/api/v1/query_range'

    def query(self, query):
        params = {
            'query': query
        }
        r = requests.get(self.url, params=params)
        try:
            return r.json()
        except Exception as e:
            logger.exception(f"query prometheus failed , resp: {r.text}", exc_info=e)
            return {"status": "success", "data": {"resultType": "matrix", "result": []}}

    def query_range(self, query, start, end, step):
        params = {
            "query": query,
            "start": start,
            "end": end,
            "step": step
        }
        r = requests.get(self.rang_url, params=params)
        logger.debug("query_prometheus result [%s] ", r.text)
        return r.json()




