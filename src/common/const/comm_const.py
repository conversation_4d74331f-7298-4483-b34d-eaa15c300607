# -*- coding: utf-8 -*-
"""
常量类 / 常量
"""
from enum import Enum
from typing import TypeVar
from zoneinfo import ZoneInfo

from sqlmodel import SQLModel

API_PREFIX = "/maas"
ModelT = TypeVar("ModelT", bound=SQLModel)
DEF_TZ_INFO = ZoneInfo("Asia/Shanghai")
DEPLOY_CUSTOM = 'CUSTOM'

DEF_PAGE_SIZE = 20  # 默认分页大小
MAX_PAGE_SIZE = 1000

INF_EVENT_QUEUE = "inf_event_queue"
PUSH_MODULE = "inference_service"
MODEL_DOWNLOAD_EVENT_QUEUE = "model_download_status"
MODEL_DOWNLOAD_PROGRESS_QUEUE = "model_download_progress"

# 公共目录
PUBLIC_MAAS_DIR_NAME = "maasfile"
PUBLIC_MODEL_DIR = f"/{PUBLIC_MAAS_DIR_NAME}/model/public"

INVALID_PORTS = [15019, 15020, 15021]


class EmptyModel(SQLModel):
    ...

class EmptyData(Enum):
    PAGE = {"response": [], "total": 0}
    LIST = []
    DATA = None


class UserPlat(str, Enum):
    """
    用户平台  云平台和ks平台
    """
    QC_CONSOLE = "qc_console"
    KS_CONSOLE = "ks_console"
    ALL = "qc_console,ks_console"


class DataOper(str, Enum):
    CREATE = "create"
    UPDATE = "update"


class ResourceModule(str, Enum):
    MODEL = "md"
    INFERENCE_SERVICE = "inf"
    JOB = "job"
    API_KEY = "sk"


class ImageType(str, Enum):
    OFFICIAL = "official"
    USER = "user"
    CUSTOM = "custom"


class ModelStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    SUSPENDED = "suspended"
    DELETED = "deleted"
    PENDING = "pending"
    DOWNLOADING = "downloading"
    DOWNLOAD_FAILED = "download_failed"


class SourceChannel(str, Enum):
    LOCAL_PATH = "local_path"
    MODEL_SCOPE = "ModelScope"
    HUGGING_FACE = "HuggingFace"
    OLLAMA = "ollama"


class InfStatus(str, Enum):
    ACTIVE = "active"
    STOP = "stopped"
    SUSPENDED = "paused"
    CEASED = "ceased"
    COMPLETED = "completed"


class InfTransitionStatus(str, Enum):
    Creating = "creating"
    STOPPING = "stopping"
    STARTING = "starting"
    UPDATING = "updating"
    DELETING = "deleting"


class InfHealthStatus(str, Enum):
    HEALTH = "health"
    UNHEALTH = "unhealth"


class PodStatus(str, Enum):
    ACTIVE = "active"
    FAILED = "failed"


class BillingAction(str, Enum):
    DESCRIBE = "describe"
    CEASE = "cease"
    SUSPEND = "suspend"
    RESUME = "resume"


class JobStatus(str, Enum):
    RUNNING = "running"
    SUSPEND = "suspend"
    SUCCESS = "success"
    FAILED = "failed"


class JobAction(str, Enum):

    # 推理服务
    DeployInference = "deploy_inference"
    StopInference = "stop_inference"
    StartInference = "start_inference"
    DeleteInference = "delete_inference"
    RebuildPod = "rebuild_pod"
    IncreasePod = "increase_pod"
    DecreasePod = "decrease_pod"

    # 模型下载
    ModelDownload = "model_download"


class AlertReason(str, Enum):
    MaaSInvokeBillingFailed = "MaaSInvokeBillingFailed"
    MaaSCheckAccountsError = "MaaSCheckAccountsError"
    MaaSUnhealthInferService = "MaaSUnhealthInferService"
    MaaSExecuteJobFailed = "MaaSExecuteJobFailed"


class InfSvcClusterType(str, Enum):
    """
    推理服务集群类型（单机、分布式）
    """
    SINGLE = 'single'
    DISTRIBUTE = 'distribute'


class JobReason(str, Enum):
    ARREARS_SUSPEND = "欠费暂停"
    ARREARS_CEASE = "欠费删除"
    ARREARS_RESUME = "欠费恢复"


class SubAccountCanConsume(int, Enum):
    """ 子账户是否可以消费 """
    NO = 0
    YES = 1


class AuthType(str, Enum):
    """权限类型"""
    READ = "read"
    WRITE = "write"


JOB_ACTION_MAPPING = {
    JobAction.DeployInference.value: "创建推理服务",
    JobAction.StopInference.value: "关闭推理服务",
    JobAction.StartInference.value: "开启推理服务",
    JobAction.DeleteInference.value: "删除推理服务",
    JobAction.RebuildPod.value: "重建实例",
    JobAction.IncreasePod.value: "推理服务扩容",
    JobAction.DecreasePod.value: "推理服务缩容",
    JobAction.ModelDownload.value: "模型下载"
}

JOB_STATUS_MAPPING = {
    JobStatus.SUCCESS.value: "succeeded",
    JobStatus.FAILED.value: "failed"
}

EVENT_MAPPING = {
    "FailedScheduling": "无法调度"
}
