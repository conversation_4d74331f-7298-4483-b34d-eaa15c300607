# -*- coding: utf-8 -*-

from typing import Optional, List, Any

from pydantic import BaseModel
from sqlmodel import SQLModel


class ProductDTO(BaseModel):
    sku_id: str = ""  # sku id
    status: str = ""  # sale
    aipods_type: str = ""  # 规格类型，only_gpu[单卡] only_cpu[CPU计算] vGPU[共享GPU] gpu-mig[MIG GPU] all_machine[整机]
    aipods_scope: str = ""  # 产品范围，inference_compute[推理服务] container_instance[容器实例] resource_group[专属资源组] sharing_compute[共享计算]
    aipods_usage: str = ""  # 资源类型（规格标签，inference-gpu、inference-cpu）
    cpu_count: int = 0  # cpu 核数
    memory: int = 0  # 内存（G）
    cpu_model: str = ""  # cpu 型号，intel 和 amd
    gpu_model: str = ""  # gpu 型号，NVIDIA 4090、NVIDIA A100、NVIDIA A800等
    gpu_count: int = 0  # gpu 个数
    gpu_memory: int = 0  # gpu 显存（G）
    os_disk: int = 0  # 系统盘大小
    disk: int = 0  # 挂载盘大小（预留）
    network: int = 0  # # IB 网络个数
    nvlink: str = ""  # 是否支持nvlink
    cpu_manufacturer: str = ""  # cpu架构


class RGProductDTO(ProductDTO):
    rg_node_id: str = ''  # 资源组节点id
    gpu_list: list[str] = []  # 可以使用的 gpu
    spec_type: str = ''


class PriceInfo(ProductDTO):
    replicas: Optional[int]


class Contract(BaseModel):
    charge_mode: str
    next_charge_mode: Optional[str]
    create_time: Optional[str]
    start_time: Optional[str]
    end_time: Optional[str]
    price_info: Optional[PriceInfo]
    discount: Optional[int]
    price: Optional[float]
    duration: Optional[str]
    auto_renew: Optional[int]


class LeaseInfo(SQLModel, table=False):
    status: str
    lease_time: Optional[str]
    status_time: str
    unlease_time: Optional[str]
    renewal_time: Optional[str]
    renewal: Optional[str]
    contract: Optional[Contract]
    status_expire_time: Optional[str]


class Price(SQLModel, table=False):
    original_price: float
    available_coupon: float
    price: float
    normal_price: float
    discount: float
    discount_details: dict
    available_coupon_set: List[Any]


class ChargeRecord(SQLModel, table=False):
    unit: str
    start_time: str
    charge_time: str
    duration: str
    fee: str
    end_time: str
    remarks: str
    discount: float
    total_sum: float
    price: str
    contract_id: str


class ChargeData(SQLModel, table=False):
    charge_record_set: List[ChargeRecord]
    total_sum: str
    total_count: int
