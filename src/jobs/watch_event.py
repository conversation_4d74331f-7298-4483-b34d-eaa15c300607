# -*- coding: utf-8 -*-
import time
from datetime import datetime, timedelta

import kr8s
import pydash

from src.common.const.comm_const import INF_EVENT_QUEUE
from src.common.loggers import task_logger as logger
from src.common.utils.data import str_to_utc_date
from src.jobs.global_job import global_task
from src.system.integrations.cache.redis_client import redis_client


def _process_event(action, resource, replicas, available_replicas, unavailable_replicas):
    """ 检测时间是否是 1 小时以内 """
    if not resource.status:
        return
    now = datetime.utcnow()
    if not pydash.find(resource.status.conditions, lambda condition: now - str_to_utc_date(condition.get('lastUpdateTime') or condition.get('lastTransitionTime')) < timedelta(hours=1)):
        return
    service_id = resource.metadata.name
    namespace = resource.metadata.namespace
    logger.info(f'事件[{namespace}/{service_id}], 动作[{action}], 状态[{resource.status}] ({replicas}/{available_replicas}/{unavailable_replicas})')
    redis_client.product_msg(INF_EVENT_QUEUE, {
        'action': action,
        'namespace': namespace,
        'service_id': service_id,
        'replicas': replicas,
        'available_replicas': available_replicas,
        'unavailable_replicas': unavailable_replicas,
    })


@global_task('监听deploy资源事件', async_exec=True)
def watch_resource_event():
    while True:
        logger.info("开始监听deploy推理服务事件...")
        try:
            for action, deploy in kr8s.watch("deployment", label_selector="maas=inference", namespace=kr8s.ALL):
                replicas = deploy.status.get("replicas", 0)
                available_replicas = deploy.status.get("availableReplicas", 0)
                unavailable_replicas = deploy.status.get("unavailableReplicas", 0)
                _process_event(action, deploy, replicas, available_replicas, unavailable_replicas)
        except Exception:  # noqa
            logger.exception('监听deploy推理服务事件异常: ')
        time.sleep(10)


@global_task('监听lws资源事件', async_exec=True)
def watch_resource_event():
    while True:
        logger.info("开始监听lws推理服务事件...")
        try:
            for action, lws in kr8s.watch("lws", label_selector="maas=inference", namespace=kr8s.ALL):
                replicas = lws.status.get('replicas')
                if replicas is None:
                    continue
                available_replicas = lws.status.get('readyReplicas', 0)
                unavailable_replicas = replicas - available_replicas
                _process_event(action, lws, replicas, available_replicas, unavailable_replicas)
        except Exception:  # noqa
            logger.exception('监听lws推理服务事件异常: ')
        time.sleep(10)
