# -*- coding: utf-8 -*-
from fastapi import APIRouter

from src.common.context import Context
from src.common.rsp_schema import DataResponse
from src.common.utils.data import wrap_rsp
from src.setting import settings

api_router = APIRouter(prefix="/api/system", tags=["平台接口"])


@api_router.get("/config")
async def get() -> DataResponse[dict]:
    """获取平台配置"""
    data = {
        "api_host": settings.PROXY_SERVER_HOST,
        "storage_type": settings.STORAGE_TYPE,
        "is_admin": Context.USER.get().is_admin
    }
    return wrap_rsp(data)
