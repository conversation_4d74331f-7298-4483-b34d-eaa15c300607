# -*- coding: utf-8 -*-
from typing import Optional

from pydantic import Field, model_validator, field_validator

from src.apps.model.req_schema import ModelImage
from src.common.const.comm_const import InfSvcClusterType, DEPLOY_CUSTOM
from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException
from src.common.req_schema import BaseReq
from src.common.utils.data import validate_port
from src.setting import settings


class ResourceGroup(BaseReq):
    """
    资源组创建推理服务请求，使用规格模板或者自定义规格
    """
    rg_id: str
    tmp_id: Optional[str] = ''

    cpu: Optional[int] = 0
    memory: Optional[int] = 0
    gpu: Optional[int] = 0
    rg_node_id: Optional[str] = ''  # 资源组节点id
    gpu_list: Optional[list[str]] = []  # 可以使用的 gpu

    gpu_name: Optional[str] = ''  # gpu 型号名称，如 NVIDIA-A100
    gpu_memory: Optional[int] = 0  # gpu 显存大小，单位 GB
    spec_type: Optional[str] = 'common'  # 规格类型 common / share_gpu / vGPU / only_cpu

    @staticmethod
    @model_validator(mode='before')
    def check_required(cls, values):
        if not values.get('rg_id'):
            raise ValueError("rg_id is required")
        if not values.get('tmp_id') and (not values.get('cpu') or not values.get('memory')):
            raise ValueError("tmp_id and (cpu or memory) is required")
        return values


class InfRunInfo(BaseReq):
    image: Optional[ModelImage] = None  # 自定义镜像
    requirements: str = Field(default=None, max_length=1024)
    port: int = Field(default=None, ge=1, le=65535)
    environment: str = Field(default=None, max_length=10240)
    run_cmd: str = Field(default=None, max_length=10240)

    @field_validator('port')
    def check_port(cls, v):  # noqa
        validate_port(v)
        return v


class CreateInfSvcReq(BaseReq):
    model_id: str
    name: str = Field(default='', max_length=64)
    sku_id: Optional[str] = ''  # 和资源组至少有一个
    resource_group: Optional[ResourceGroup] = None

    engine_id: str = ''  # 推理引擎
    run_info: Optional[InfRunInfo] = ''  # 推理服务运行参数

    cluster_type: InfSvcClusterType
    instance_num: int = Field(..., ge=1)
    node_num: Optional[int] = Field(default=1, ge=1)

    @model_validator(mode='after')
    def check_required(self):
        if not self.sku_id and not self.resource_group:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="请设置资源规格")
        if self.cluster_type == InfSvcClusterType.SINGLE and self.node_num > 1:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="单节点推理服务不支持设置节点数")
        if self.cluster_type == InfSvcClusterType.DISTRIBUTE and self.node_num < 2:
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="分布式推理服务至少设置两个节点")
        if self.engine_id != DEPLOY_CUSTOM and self.engine_id not in settings.INF_ENGINE.keys():
            raise MaaSBaseException(Err.VALIDATE_PARAMS, message="推理引擎不存在")
        return self


class ScaleServiceReq(BaseReq):
    num: int


class UpdateServiceReq(BaseReq):
    name: str
