# -*- coding: utf-8 -*-
import os
import unittest

import kr8s
import pydash
from kr8s.objects import Deployment

from src.common.k8s_resource.costom_object import LeaderWorkerSetObject

os.environ['ENV_CONF'] = 'dev'

class MyTestCase(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self) -> None:
        client = await kr8s.api(kubeconfig=os.path.join(os.path.dirname(os.path.dirname(__file__)), "local/config.yaml"))
        self.client = client

    async def test_get_event(self):
        ns = 'usr-xqlpq3qv'
        pod = 'inf-kuhq1wnn-589fc78d96-sz5l7'

        event = kr8s.get("events", namespace=ns, field_selector=f"involvedObject.name={pod}")
        for i in event:
            print(i)

    async def test_get_deploy(self):
        namespace = 'usr-xqlpq3qv'
        service_id = 'inf-uruca9xi'

        deploy = Deployment.get(service_id, namespace=namespace)
        print(deploy)

        for pod in deploy.pods():
            if pod.status.get('phase') == 'Terminating':
                print('删除中')

            containers = pod.status.get("containerStatuses", [])
            inf_container = pydash.find(containers, lambda c: c.name == 'inference-container')

            if not inf_container or not inf_container.ready:
                pod_status = 'failed'
            else:
                pod_status = 'active'
            restart_count = inf_container.restartCount if inf_container else 0

            print(pod_status, restart_count)


    async def test_get_pod(self):
        namespace = 'usr-ohn3pioq'
        service_id = 'inf-kqoiks82'

        pods = kr8s.get('pod', namespace=namespace, label_selector=f'app={service_id}')
        for pod in pods:
            print(pod.name)
            print(pod.status.phase)

    async def test_lws(self):
        lws = LeaderWorkerSetObject.get('inf-6w2bawbv', namespace='usr-ohn3pioq')
        # Box({'conditions': [{'lastTransitionTime': '2025-04-29T08:14:34Z', 'message': 'Replicas are progressing',
        # 'reason': 'GroupsPro...eaderworkerset.sigs.k8s.io/name=inf-6w2bawbv,leaderworkerset.sigs.k8s.io/worker-index=0',
        # 'replicas': 2, 'updatedReplicas': 1})
        print(lws.status)
    #  ready_count.update({v.name: v.status.get("availableReplicas", 0) for v in deploys})  # noqa