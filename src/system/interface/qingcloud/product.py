# -*- coding: utf-8 -*-
import json

from src.apps.billing.rsp_schema import ProductDTO
from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException
from src.setting import settings
from src.system.interface.abs_product_interface import AbsProductInterface
from src.system.interface.qingcloud.iaas_client import iaas_client


PRODUCTION_PREFIX = "qai-"


class QingcloudProduct(AbsProductInterface):

    def get_resource_id(self, product_id) -> str:
        return product_id.removeprefix(PRODUCTION_PREFIX)

    def get_product_by_id(self, product_id: str) -> ProductDTO:
        params = {
            "prod_id": "qai",
            "console_id": settings.QINGCLOUD_CONSOLE_ID,
            "region_id": [settings.QINGCLOUD_REGION],
            # "status": ["sale", "no_sale"],
            "field_mask": ["price"],
            "version": "latest",
            "spec_id": "aipods",
            "search_word": product_id,
        }

        ret = iaas_client.send_request("ProductCenterQueryRequest", {
            "action": "ProductCenterQueryRequest",
            "path": "/v1/skus:search",
            "method": "POST",
            "params": json.dumps(params)
        })
        if not ret.get("skus"):
            raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=product_id)

        sku_data = ret.get("skus")[0]
        product = {
            "sku_id": sku_data["sku_id"],
            "status": sku_data["status"],
        }
        for item in sku_data.get("filters"):
            if item["attr_id"] in ProductDTO.__fields__:
                product[item["attr_id"]] = item["attr_value"]

        return ProductDTO(**product)


qingcloud_product = QingcloudProduct()
