# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional, List

from pydantic import computed_field
from sqlalchemy.dialects.postgresql import JSON
from sqlmodel import SQLModel, Field

from src.system.interface import PI


class InfSvcBase(SQLModel):
    id: str = Field(primary_key=True)
    name: str
    model_id: str
    model: str
    status: str
    intranet_url: Optional[str] = ''
    external_url: Optional[str] = ''
    spec_info: Optional[dict] = Field(default=None, sa_type=JSON)
    instance_num: int
    node_num: int
    health_status: Optional[str] = ''
    transition_status: Optional[str] = ''
    cluster_type: str
    engine_id: Optional[str] = ''

    api_key: Optional[str] = ''
    run_info: Optional[dict] = Field(default=None, sa_type=JSON)
    resource_group_id: Optional[str] = ''
    creator: str
    create_time: Optional[datetime]
    update_time: Optional[datetime]

    @computed_field
    @property
    def running_pod_num(self) -> int:
        return getattr(self, '_running_pod_num', 0)

    @running_pod_num.setter
    def running_pod_num(self, value):
        self._running_pod_num = value  # noqa

    @computed_field
    @property
    def pod_event(self) -> dict[str, str]:
        return getattr(self, '_pod_event', {})

    @pod_event.setter
    def pod_event(self, value):
        self._pod_event = value  # noqa

    @computed_field
    def creator_name(self) -> str:
        return PI.user_interface.get_user_by_id(self.creator).user_name


class InfSvcDetail(InfSvcBase):
    ...


class InfSvc(InfSvcDetail, table=True):
    __tablename__ = "inference_service"


class PodStatusRsp(SQLModel):
    pod_id: str
    restart_count: int
    pod_status: str
    create_time: str


class PodInfoRsp(SQLModel):
    pod_num: int
    running_pod_num: int
    pods: List[PodStatusRsp]


