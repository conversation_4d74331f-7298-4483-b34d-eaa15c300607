stages:
  - build

variables:
    DOCKER_DRIVER: overlay2

image: docker
services:
  - name: docker:dind
    command: ["--tls=false", "--insecure-registry=$REGISTRY_HOST_TEST"]

before_script:
  - unset HTTPS_PROXY
  - unset HTTP_PROXY

# ******************************************** build *******************************************
test-build:
  stage: build
  only:
    - dev
  script:
    - docker build -t $REGISTRY_HOST_KS/aicp/model-manage-server:dev --push .
product-build:
  stage: build
  only:
    - tags
  script:
    - docker buildx create --name mybuilder --use;
    - docker buildx build --platform linux/amd64,linux/arm64 --builder mybuilder -t $REGISTRY_HOST_KS/aicp/model-manage-server:$CI_COMMIT_TAG --push .