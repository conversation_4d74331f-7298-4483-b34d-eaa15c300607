from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Request, Depends, Query, Response

from src.apps.depends import check_permission
from src.apps.inference_service.curd import inf_svc_curd
from src.apps.inference_service.req_schema import ScaleServiceReq
from src.apps.inference_service.req_schema import UpdateServiceReq, CreateInfSvcReq
from src.apps.inference_service.rsp_schema import InfSvc, InfSvcBase, InfSvcDetail, PodInfoRsp
from src.common.const.comm_const import InfStatus, UserPlat
from src.common.const.err_const import Err
from src.common.dto import QueryDTO, INF_READ, INF_WRITE
from src.common.dynamic_filter import param_to_query
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger
from src.common.rsp_schema import DataResponse, R, PageResponse, IDResponse
from src.common.utils.data import wrap_rsp

api_router = APIRouter(prefix="/api/inference_service", tags=["推理服务"], dependencies=[Depends(check_permission(UserPlat.ALL))])
admin_router = APIRouter(prefix="/admin/inference_service", tags=["推理服务(管理端)"], dependencies=[Depends(check_permission(UserPlat.KS_CONSOLE))])
inf_svc_router = APIRouter(prefix="/api/inf-svc", tags=["推理服务(权限)"])

@api_router.post("")
async def create(inf_svc_req: CreateInfSvcReq) -> IDResponse:
    service_id = await inf_svc_curd.create(inf_svc_req)
    return wrap_rsp(service_id)


@admin_router.get("/usage")
async def get_svc_usage(user_ids: list[str] = Query()) -> DataResponse[dict[str, int]]:
    return wrap_rsp(await inf_svc_curd.get_svc_usage(user_ids))


@param_to_query(
    keyword_fields=["id", "name"],
    eq=["status"],
    sort_fields=["create_time", "update_time"],
    table_type=InfSvc,
    exclude_status=[InfStatus.CEASED.value, InfStatus.COMPLETED.value],
    filter_creator='SUB_ACCOUNT',
)
async def list_param(request: Request):
    ...


@api_router.get("")
async def list_(query_dto: QueryDTO = Depends(list_param)) -> PageResponse[InfSvcBase]:
    _list, total = await inf_svc_curd.get_by_query_dto(query_dto, with_count=True)
    await inf_svc_curd.fill_svc_info(_list)
    return wrap_rsp(_list, total=total)


@api_router.get("/{service_id}", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def get_(service_id: str) -> DataResponse[InfSvcDetail]:
    data = await inf_svc_curd.get_by_id(service_id)
    if not data or data.status in [InfStatus.CEASED.value, InfStatus.COMPLETED.value]:
        raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{service_id}")
    await inf_svc_curd.fill_svc_info([data])
    return wrap_rsp(data)


@api_router.get("/{service_id}/pods", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def get_svc_instance(service_id: str, sort_key: str = "create_time", reverse: int = 0, status: Optional[str] = None) -> DataResponse[PodInfoRsp]:
    """
    推理服务 pod 信息查询
    :param service_id: 模型服务id
    :param status: 服务状态
    :param sort_key: 排序字段
    :param reverse: 是否降序
    """
    return wrap_rsp(await inf_svc_curd.get_svc_pods(service_id, status, sort_key, reverse))


@api_router.put("/{service_id}/stop", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def stop(service_id: str):
    """
    用户端关闭服务
    :param service_id: 模型服务id
    """
    await inf_svc_curd.stop(service_id)
    return wrap_rsp()


@api_router.put("/{service_id}/start", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def start(service_id: str):
    """
    用户端开启服务
    :param service_id: 模型服务id
    """
    await inf_svc_curd.start(service_id)
    return wrap_rsp()


@api_router.delete("/{service_id}", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def delete(service_id: str):
    """
    用户端删除服务
    :param service_id: 模型服务id
    """
    await inf_svc_curd.delete(service_id)
    return wrap_rsp()


@api_router.put("/{service_id}/scale", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def scale(service_id: str, scale_req: ScaleServiceReq):
    """
    用户端扩容服务
    :param service_id: 模型服务id
    :param scale_req: 扩容请求
    """
    await inf_svc_curd.scale(service_id, scale_req.num)
    return wrap_rsp()


@api_router.get("/{service_id}/log", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def get_pod_log(service_id: str, instance_id: Optional[str] = None, key_word: Optional[str] = None,
                      start_time: Optional[datetime] = None, end_time: Optional[datetime] = None,
                      size: int = 10000, page: int = 0, reverse: bool = False) -> PageResponse[dict]:
    """
    获取推理服务日志
    :param service_id: 服务ID
    :param instance_id: 实例ID
    :param key_word: 关键词
    :param start_time: 开始时间（UTC时间）
    :param end_time: 结束时间（UTC时间）
    :param size: 每页大小
    :param page: 页码
    :param reverse: 是否倒序
    :return: 日志列表
    """
    data = await inf_svc_curd.get_by_id(service_id)
    if not data:
        raise MaaSBaseException(Err.NOT_FOUND)
    if data.status in [InfStatus.CEASED.value]:
        return wrap_rsp([], total=0)
    namespace = data.creator.lower()
    logs, total = await inf_svc_curd.get_log(service_id, instance_id, key_word, start_time, end_time, namespace, size=size, page=page, reverse=reverse)
    return wrap_rsp(logs, total=total)


@api_router.post("/{service_id}/{pod_id}/rebuild", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def rebuild_pod(service_id: str, pod_id: str):
    data = await inf_svc_curd.get_by_id(service_id)
    if not data:
        raise MaaSBaseException(Err.NOT_FOUND)
    if data.status in [InfStatus.CEASED.value] or data.transition_status:
        raise MaaSBaseException(Err.AUTH_INSUFFICIENT)
    await inf_svc_curd.rebuild_pod(data, pod_id)
    return wrap_rsp()


@api_router.put("/{service_id}", dependencies=[Depends(check_permission(valid_owner=INF_WRITE))])
async def update(service_id: str, update_req: UpdateServiceReq):
    await inf_svc_curd.base_update(service_id, update_req.model_dump(), strict=True)
    return wrap_rsp()


@api_router.get("/{service_id}/download_log", dependencies=[Depends(check_permission(valid_owner=INF_READ))])
async def download_pod_log(service_id: str, instance_id: Optional[str] = None, key_word: Optional[str] = None,
                          start_time: Optional[datetime] = None, end_time: Optional[datetime] = None,
                          reverse: bool = False):
    """
    下载推理服务日志
    :param service_id: 服务ID
    :param instance_id: 实例ID
    :param key_word: 关键词
    :param start_time: 开始时间（UTC时间）
    :param end_time: 结束时间（UTC时间）
    :param reverse: 是否倒序
    :return: 日志文件流
    """
    # 生成文件名
    timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
    filename = f"logs_{service_id}_{timestamp}.log"

    # 获取日志内容
    log_content, _ = await inf_svc_curd.get_log(
        service_id=service_id,
        instance_id=instance_id,
        key_word=key_word,
        start_time=start_time,
        end_time=end_time,
        reverse=reverse,
        stream=True
    )

    # 创建响应
    return Response(
        content=log_content,
        media_type="text/plain",
        headers={
            "Content-Disposition": f"attachment; filename={filename}",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )


@inf_svc_router.get("/auth")
async def validate_auth(path: str, api_key: str):
    # /jinan1/inference/usr-ohn3pioq/inf-lq5p4ppt/xxx
    # 不做权限校验的请求
    if path.endswith('/openapi.json'):
        return R.suc()
    path_arr = path.split('/')
    inf_id = path_arr[4] if len(path_arr) > 4 else ''
    inf_api_key = await inf_svc_curd.get_service_api_key(inf_id)
    if inf_api_key and f'Bearer {inf_api_key}' != api_key:
        logger.warning(f'[推理接口] [{path}] 用户令牌 [{api_key}] 和服务令牌 [{inf_api_key}] 不匹配')
        raise MaaSBaseException(Err.AUTH_INSUFFICIENT)
    return R.suc()