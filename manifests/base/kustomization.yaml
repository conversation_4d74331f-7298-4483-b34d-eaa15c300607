apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

sortOptions:
  order: legacy
  legacySortOptions:
    orderFirst:
      - ClusterRole
      - Role
      - ServiceAccount
      - ClusterRoleBinding
      - RoleBinding
      - Deployment
      - Service

resources:
  - namespace.yaml
  - maas-img-secret.yaml
  - deployment.yaml
  - service-account.yaml
  - service.yaml
  - virtual-service.yaml
  - maas-gateway.yaml
  - inference-gateway.yaml
  - destination-rule.yaml
  - authorizationpolicy.yaml
  - cluster-role.yaml
  - cluster-role-binding.yaml
  - servicemonitor.yaml
#  - role.yaml
#  - role-binding.yaml

namespace: maas-system
commonLabels:
  app: model-manage-server
  kustomize.component: model-manage-server

configMapGenerator:
  - files:
      - config.yaml
    name: model-manage-server-config

generatorOptions:
  disableNameSuffixHash: true

configurations:
  - params.yaml
