# -*- coding: utf-8 -*-
"""
常量类 / 常量
"""
from enum import Enum


class URLPrefix(str, Enum):
    data = "data"


url_reflection_type = {
    "model": "maas",
    "model_version": "maas",
    "inference_service": "maas",
}


def need_get_response(first_segment: str, last_segment: str, method: str) -> bool:
    """

    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :return:
    """
    if first_segment == "inference_service" and last_segment == "rebuild":
        return False
    if method == "DELETE" or method == "PUT":
        return False
    else:
        return True

