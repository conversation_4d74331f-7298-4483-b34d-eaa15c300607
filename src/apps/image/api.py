# -*- coding: utf-8 -*-
from fastapi import APIRouter

from src.apps.image.curd import image_curd
from src.apps.image.rsp_schema import Image
from src.common.rsp_schema import ListResponse
from src.common.utils.data import wrap_rsp

api_router = APIRouter(prefix="/api/image", tags=["推理服务镜像接口"])


@api_router.get("")
async def list_() -> ListResponse[Image]:
    return wrap_rsp(list(image_curd.list().values()))
