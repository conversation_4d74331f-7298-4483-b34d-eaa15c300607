import logging
from src.common.context import Context

from src.setting import settings

formatter = logging.Formatter(
    '%(asctime)s-%(levelname)s-[%(process)d %(thread)d %(threadName)s]-[%(name)s] %(pathname)s:%(lineno)d-%(message)s(%(traceid)s)'
)


class TraceIDFilter(logging.Filter):
    def filter(self, record):
        record.traceid = Context.TRACE_ID.get() or 'no-trace'
        return True


handler = logging.StreamHandler()
handler.setFormatter(formatter)

logger = logging.getLogger("MAIN")
logger.setLevel(logging.getLevelName(settings.LOGGING_LEVEL))
logger.addHandler(handler)
logger.addFilter(TraceIDFilter())

task_logger = logging.getLogger("TASK")
task_logger.setLevel(logging.getLevelName(settings.LOGGING_LEVEL))
task_logger.addHandler(handler)
task_logger.addFilter(TraceIDFilter())