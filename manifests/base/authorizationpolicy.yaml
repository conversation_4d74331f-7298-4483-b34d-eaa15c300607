apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: model-manage-server
spec:
  action: ALLOW
  rules:
    - from:
        - source:
            principals:
              - cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account
              - cluster.local/ns/aicp-system/sa/aicp-service-account
    - to:
      - operation:
          paths:
          - /maas/api/inf-svc/auth
  selector:
    matchLabels:
      app: model-manage-server
