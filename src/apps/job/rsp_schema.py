# -*- coding: utf-8 -*-
from datetime import datetime

from sqlmodel import SQLModel, Field

class JobBase(SQLModel):
    job_id: str = Field(primary_key=True)
    resource_id: str
    action: str
    status: str
    creator: str
    reason: str
    create_time: datetime
    update_time: datetime


class Job(JobBase, table=True):
    __tablename__ = "job"

    src_replica: int
    target_replica: int
    remark: str = ''
