import json
from typing import Any

from fastapi import Request, Response
from fastapi.responses import StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.common.context import Context
from src.common.loggers import logger
from src.middlewares.operation.reflection_api import need_get_response, url_reflection_type
from src.middlewares.operation.utils import remove_prefix, get_resource_id, generate_resource_redis_data
from src.system.integrations.cache.redis_client import producer


class OperationRecordMiddleware(BaseHTTPMiddleware):

    async def dispatch(self, request: Request, call_next):
        """
        Process request and response, including logging request/response body and handling streaming responses.
        """
        # 解析请求信息
        url = str(request.url.path)
        url, first_segment, last_segment = self._parse_request_url(url, True)
        if first_segment == "api":
            url, first_segment, last_segment = self._parse_request_url(url, True, "/api")
            user_id, operation_user_id = self._get_user_info(request, first_segment)
            request_body_data, request = await self._parse_request_body(request, first_segment, last_segment)

            # 调用下一个中间件或路由处理函数
            response = await call_next(request)

            # 处理响应
            return await self._process_response(request, response, url, first_segment, last_segment,
                                                user_id, operation_user_id, request_body_data)
        else:
            response = await call_next(request)
            return response

    def _parse_request_url(self, url: str, remove_prefix_flag: bool = False, prefix: str = None) -> tuple:
        """
        解析请求URL，提取并返回URL路径的关键部分
        Args:
            url (str): 要解析的URL路径
            remove_prefix_flag (bool): 是否移除前缀，默认为False
            prefix (str): 要移除的前缀，默认为None
        Returns:
            tuple: 包含三个元素的元组
                - url (str): 处理后的URL路径
                - first_segment (str): URL路径的第一个段
                - last_segment (str): URL路径的最后一个段
        """
        logger.debug(f"Original URL: {url}")

        # 如果需要移除前缀
        if remove_prefix_flag:
            if prefix:
                url = remove_prefix(url, prefix)
            else:
                url = remove_prefix(url)
            logger.debug(f"URL after removing prefix '{prefix}': {url}")

        # 统一处理路径分段
        path_segments = url.strip("/").split("/")
        first_segment = path_segments[0] if path_segments else None
        last_segment = path_segments[-1] if path_segments else None

        logger.debug(f"First segment: {first_segment}, Last segment: {last_segment}")
        return url, first_segment, last_segment


    def _get_user_info(self, request: Request, first_segment: str) -> tuple:
        """
        从请求中提取用户信息

        Args:
            request (Request): FastAPI请求对象
            first_segment (str): URL路径的第一个段

        Returns:
            tuple: 包含两个元素的元组
                - user_id (str): 用户ID
                - operation_user_id (str): 操作用户ID
        """
        user_id = None
        if request.headers.get("X-Remote-Group") == "system:authenticated" and request.headers.get(
                "X-Remote-User") == "admin":
            user_id = "admin"
        else:
            user_id = request.headers.get("aicp-userid")

        logger.debug(f"User: {user_id}")

        operation_user_id = user_id
        return user_id, operation_user_id

    async def _parse_request_body(self, request: Request, first_segment: str, last_segment: str):
        """
        解析请求体数据

        Args:
            request (Request): FastAPI请求对象
            first_segment (str): URL路径的第一个段

        Returns:
            bytes: 请求体数据，如果不需要解析则返回None
        """
        cached_body = None
        if first_segment in url_reflection_type and request.method in ["POST", "PUT", "PATCH"]:
            # request_body_data = await request.body()
            cached_body = await request.body()

            # 保留原始接收函数
            original_receive = request._receive

            # 创建可重复读取的生成器
            class BodyReplay:
                def __init__(self, body: bytes):
                    self.body = body
                    self.consumed = False

                async def __aiter__(self):
                    if not self.consumed:
                        self.consumed = True
                        yield self.body

            # 重写请求对象的核心方法
            request._body = cached_body  # 直接设置_body属性
            request.stream = lambda: BodyReplay(cached_body)  # 类型: ignore

            # 保持下游兼容性的关键补丁
            async def patched_receive() -> dict[str, Any]:
                return {"type": "http.request", "body": cached_body, "more_body": False}

            request._receive = patched_receive
            logger.debug(f"Parsed request body: {cached_body}")
        return cached_body, request

    async def _process_response(self, request: Request, response: Response, url: str,
                                first_segment: str, last_segment: str, user_id: str,
                                operation_user_id: str, request_body_data) -> Response:
        """
        处理响应并记录操作日志

        Args:
            request (Request): FastAPI请求对象
            response (Response): FastAPI响应对象
            url (str): 请求URL路径
            first_segment (str): URL路径的第一个段
            last_segment (str): URL路径的最后一个段
            user_id (str): 用户ID
            operation_user_id (str): 操作用户ID
            request_body_data (bytes): 请求体数据

        Returns:
            Response: 处理后的响应对象
        """
        trace_id = Context.TRACE_ID.get()
        logger.debug(f"trace id: {trace_id}")

        # 不在白名单里的不处理
        if first_segment not in url_reflection_type:
            return response
        # 如果是GET请求
        if request.method == "GET":
            return response

        need_response = need_get_response(first_segment, last_segment, request.method)
        params = request.query_params
        if not (first_segment == "inference_service" and last_segment in ["start", "stop", "scale", "rebuild"]):
            last_segment = first_segment
        if  request.method in ["PUT", "DELETE"] or (request.method == "POST" and first_segment == "inference_service" and last_segment == "rebuild"):
            params = request.path_params
        logger.debug(f"params : {params}")

        if not need_response:
            await self._process_normal_response(request, response, url, first_segment, last_segment,
                                                user_id, operation_user_id, trace_id, params, request_body_data)
        else:
            response = await self._process_streaming_response(request, response, url, first_segment, last_segment,
                                                              user_id, operation_user_id, trace_id, params)

        return response

    async def _process_normal_response(self, request: Request, response: Response, url: str,
                                       first_segment: str, last_segment: str, user_id: str,
                                       operation_user_id: str, trace_id: str, params, request_body_data):
        """
        处理普通响应并记录操作日志

        Args:
            request (Request): FastAPI请求对象
            response (Response): FastAPI响应对象
            url (str): 请求URL路径
            first_segment (str): URL路径的第一个段
            last_segment (str): URL路径的最后一个段
            user_id (str): 用户ID
            operation_user_id (str): 操作用户ID
            trace_id (str): 请求跟踪ID
            params: 请求参数
            request_body_data (bytes): 请求体数据
        """
        try:
            logger.debug(f"Parsed request method: {request.method}")
            logger.debug(f"Parsed params: {params}")
            logger.debug(f"Parsed request body: {request_body_data}")
            ids = get_resource_id(first_segment, last_segment, request.method, params, request_body_data)
            logger.debug(f"id list: {ids}")
            for id in ids:
                operation_data = generate_resource_redis_data(url, first_segment, last_segment, request.method, id,
                                                              user_id,
                                                              operation_user_id, trace_id)
                logger.info(f"operation data: {operation_data}")
                await producer(operation_data)
        except Exception as e:
            logger.error(f"Error processing request: {e}")

    async def _process_streaming_response(self, request: Request, response: Response, url: str,
                                          first_segment: str, last_segment: str, user_id: str,
                                          operation_user_id: str, trace_id: str, params) -> Response:
        """
        处理流式响应并记录操作日志

        Args:
            request (Request): FastAPI请求对象
            response (Response): FastAPI响应对象
            url (str): 请求URL路径
            first_segment (str): URL路径的第一个段
            last_segment (str): URL路径的最后一个段
            user_id (str): 用户ID
            operation_user_id (str): 操作用户ID
            trace_id (str): 请求跟踪ID
            params: 请求参数

        Returns:
            Response: 处理后的流式响应对象
        """
        if not hasattr(response, "body_iterator"):
            return await self._process_non_streaming_response(request, response, url, first_segment, last_segment,
                                                              user_id, operation_user_id, trace_id, params)

        # 缓存流式数据
        original_iterator = response.body_iterator

        async def log_stream():
            stream_content = b""
            try:
                async for chunk in original_iterator:
                    logger.debug(f"Stream chunk: {chunk.decode()}")
                    stream_content += chunk
                    yield chunk
                await self._process_stream_content(stream_content, url, first_segment, last_segment,
                                                   request.method, params, user_id, operation_user_id, trace_id)
            except Exception as e:
                logger.error(f"Error processing stream: {e}")

        return StreamingResponse(
            content=log_stream(),
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.media_type,
        )

    async def _process_non_streaming_response(self, request: Request, response: Response, url: str,
                                              first_segment: str, last_segment: str, user_id: str,
                                              operation_user_id: str, trace_id: str, params) -> Response:
        """
        处理非流式响应并记录操作日志

        Args:
            request (Request): FastAPI请求对象
            response (Response): FastAPI响应对象
            url (str): 请求URL路径
            first_segment (str): URL路径的第一个段
            last_segment (str): URL路径的最后一个段
            user_id (str): 用户ID
            operation_user_id (str): 操作用户ID
            trace_id (str): 请求跟踪ID
            params: 请求参数

        Returns:
            Response: 处理后的响应对象
        """
        body = await response.body()
        try:
            json_body = json.loads(body.decode())
            formatted_body = json.dumps(json_body, indent=4, ensure_ascii=False)
            logger.debug(f"Response body (formatted JSON):\n{formatted_body}")
            ids = get_resource_id(first_segment, last_segment, request.method, params, formatted_body)
            logger.debug(f"id list: {ids}")
            for id in ids:
                operation_data = generate_resource_redis_data(url, first_segment, last_segment, request.method, id,
                                                              user_id, operation_user_id, trace_id)
                logger.info(f"operation data: {operation_data}")
                await producer(operation_data)
        except json.JSONDecodeError:
            logger.error(f"Response body (raw): {body.decode()}")
        return response

    async def _process_stream_content(self, stream_content: bytes, url: str, first_segment: str,
                                      last_segment: str, method: str, params, user_id: str,
                                      operation_user_id: str, trace_id: str):
        """
        处理流式内容并记录操作日志

        Args:
            stream_content (bytes): 流式响应内容
            url (str): 请求URL路径
            first_segment (str): URL路径的第一个段
            last_segment (str): URL路径的最后一个段
            method (str): HTTP方法
            params: 请求参数
            user_id (str): 用户ID
            operation_user_id (str): 操作用户ID
            trace_id (str): 请求跟踪ID
        """
        try:
            json_stream_content = json.loads(stream_content.decode())
            formatted_content = json.dumps(json_stream_content, indent=4, ensure_ascii=False)
            logger.debug(f"Full stream content (formatted JSON):\n{formatted_content}")
            ids = get_resource_id(first_segment, last_segment, method, params, formatted_content)
            logger.debug(f"id list: {ids}")
            for id in ids:
                operation_data = generate_resource_redis_data(url, first_segment, last_segment, method, id,
                                                              user_id, operation_user_id, trace_id)
                logger.info(f"operation data: {operation_data}")
                await producer(operation_data)
        except json.JSONDecodeError:
            logger.error(f"Full stream content (raw): {stream_content.decode()}")
