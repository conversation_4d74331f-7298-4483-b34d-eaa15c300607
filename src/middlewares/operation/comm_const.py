# -*- coding: utf-8 -*-
"""
常量类 / 常量
"""

MODEL_OPERATION = {
    "model_POST": "maas_create_model",
    "model_DELETE": "maas_delete_model",
    "model_PUT": "maas_update_model",
}


MODEL_VERSION_OPERATION = {
    "model_version_POST": "maas_create_model_version",
    "model_version_DELETE": "maas_delete_model_version",
    "model_version_PUT": "maas_update_model_version",

}

INFERENCE_SERVICE_OPERATION = {
    "inference_service_POST": "maas_create_inference_service",
    "inference_service_DELETE": "maas_delete_inference_service",
    "inference_service_PUT": "maas_update_inference_service",
    "start_PUT": "maas_start_inference_service",
    "stop_PUT": "maas_stop_inference_service",
    "scale_PUT": "maas_scale_inference_service",
    "rebuild_POST": "maas_rebuild_inference_service",
}


OPERATION = {
    "model": MODEL_OPERATION,
    "model_version": MODEL_VERSION_OPERATION,
    "inference_service": INFERENCE_SERVICE_OPERATION,
}