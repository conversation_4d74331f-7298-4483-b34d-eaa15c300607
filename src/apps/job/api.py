# -*- coding: utf-8 -*-
from fastapi import APIRouter, Request, Depends

from src.apps.depends import check_permission
from src.apps.job.curd import job_curd
from src.apps.job.rsp_schema import Job, JobBase
from src.common.const.comm_const import JOB_ACTION_MAPPING, UserPlat
from src.common.dto import QueryDTO
from src.common.dynamic_filter import param_to_query
from src.common.rsp_schema import PageResponse
from src.common.utils.data import wrap_rsp

api_router = APIRouter(prefix="/api/job", tags=["任务管理"], dependencies=[Depends(check_permission(UserPlat.ALL))])


@param_to_query(
    sort_fields=["create_time", "update_time"],
    eq=["status", "resource_id"],
    gte=["create_time"],
    lte=["create_time"],
    table_type=Job,
    filter_creator='SUB_ACCOUNT',
)
async def list_api_page(request: Request):
    ...


@api_router.get("")
async def list_user_page(query_dto: QueryDTO = Depends(list_api_page)) -> PageResponse[JobBase]:
    data, total = await job_curd.get_by_query_dto(query_dto, with_count=True)
    for v in data:
        v.action = JOB_ACTION_MAPPING.get(v.action, v.action)
    return wrap_rsp(data, total=total)

