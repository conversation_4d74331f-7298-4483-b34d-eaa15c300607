FROM python:3.11.7-slim-bullseye

WORKDIR /code

COPY ./requirements.txt /code/requirements.txt

RUN apt-get update && apt-get install -y libmagic1 curl wget vim net-tools telnet iputils-ping

RUN pip install --no-cache-dir --upgrade -i https://mirrors.aliyun.com/pypi/simple/ -r /code/requirements.txt

COPY . ./

EXPOSE 5001

ENTRYPOINT ["gunicorn", "src.app:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:5001"]

