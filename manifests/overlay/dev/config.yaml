db_connection_str: postgresql+psycopg2://aicp:Hpcadmin123@*************:30206/maas
inference_service_host: https://hpc-ai.qingcloud.com/jinan1/inference/{namespace}/{service}/
docker_registry: j1-dockerhub.qingcloud.com
repo_project: aicp

qingcloud_access_key_id: RNVYQQVFYQIRIPXMCIWJ
qingcloud_secret_access_key: ec0tHNu2Ze1juB7wflR3cFbbVsqyECY2WIcqTBUG
qingcloud_zone: jinan1
qingcloud_host: api.qingcloud.com
qingcloud_port: 443
qingcloud_protocol: https
qingcloud_console_id: qingcloud
qingcloud_region: jinan1

opensearch_host: https://*************:30920
billing_enable: 1

redis_host: **************
prometheus_server: *************:32610
aicp_istio_server: http://*************:31080
super_accounts: ['usr-ohn3PIoq']
model_download_img: hub.kubesphere.com.cn/aicp/model-download-server:dev
repo_proxy:
  api_url: https://hub.kubesphere.com.cn/api/v2.0
  user: aicp
  pass: Aicp@123
  project: public