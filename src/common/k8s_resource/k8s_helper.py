# -*- coding: utf-8 -*-
import re

from kr8s import NotFoundError
from kr8s.objects import APIObject

from src.common.loggers import logger


def create_k8s_res(api_obj: APIObject | object, yaml_tmpl: dict, ignore_err=False):
    try:
        resource = api_obj(yaml_tmpl)  # noqa
        resource.create()
        logger.debug(f'创建资源[{api_obj.name}]成功')
    except Exception as e:
        if not ignore_err:
            logger.exception(f'创建资源失败[{yaml_tmpl}]:')
            raise e


def del_k8s_res(api_obj: APIObject | object, name, namespace):
    try:
        _obj = api_obj.get(name, namespace)
        _obj.delete()
        logger.debug(f'资源[{namespace} {name}]删除成功')
    except NotFoundError as e:
        ...
    except Exception as e:
        logger.exception(f'资源[{namespace} {name}]删除失败')
        raise e


def to_k8s_rfc_1123(name):
    # 删除非允许的字符
    name = re.sub(r'[^a-z0-9-]', '', name.lower())
    # 删除开头和结尾的连字符
    name = re.sub(r'^-|-$', '', name)
    # 确保名字长度不超过63个字符
    name = name[:63]
    return name