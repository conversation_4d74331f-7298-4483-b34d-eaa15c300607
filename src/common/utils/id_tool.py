# -*- coding: utf-8 -*-
import random
import string
from typing import Optional

from src.common.const.comm_const import ResourceModule


def uuid(prefix: Optional[ResourceModule], exclude_uppercase: bool = False, length=8) -> str:
    ascii_letters = string.ascii_lowercase + string.digits
    if not exclude_uppercase:
        ascii_letters += string.ascii_uppercase
    uuid_str = ''.join(random.choice(ascii_letters) for _ in range(length))
    return f"{prefix.value}-{uuid_str}" if prefix else uuid_str
