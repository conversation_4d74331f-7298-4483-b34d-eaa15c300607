# -*- coding: utf-8 -*-
from datetime import datetime
from typing import List, Optional

import kr8s
import pydash
from cachetools import TTLCache
from kr8s.objects import Pod

from src.apps.base_curd import BaseCURD
from src.apps.billing.curd import BillingCURD, DEF_RES_ID
from src.apps.billing.rsp_schema import RGProductDTO
from src.apps.image.curd import image_curd
from src.apps.inference_service.req_schema import CreateInfSvcReq
from src.apps.inference_service.rsp_schema import InfSvc
from src.apps.job.curd import job_curd
from src.apps.model.curd import model_curd
from src.apps.model.req_schema import ModelImage
from src.apps.model.rsp_schema import Model
from src.common.asyncache import cached
from src.common.const.comm_const import ResourceModule, InfStatus, InfTransitionStatus, JobAction, ImageType, \
    EVENT_MAPPING, JobReason, SubAccountCanConsume, PUBLIC_MODEL_DIR, DEPLOY_CUSTOM
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.dto import User, KS_ADMIN_USER
from src.common.exceptions import MaaSBaseException
from src.common.k8s_resource.base_operation import OperationManager
from src.common.loggers import logger
from src.common.utils.id_tool import uuid
from src.setting import settings
from src.system.db.sync_db import session_manage
from src.system.integrations.aicpserver import aicp_client
from src.system.integrations.aicpserver.aicp_client import AuthType
from src.system.integrations.logging.client import opensearch_client
from src.system.integrations.push.push_client import push_client
from src.system.interface import PI

DELETED_STATUS = [InfStatus.CEASED.value, InfStatus.COMPLETED.value]

NON_STATUS = (InfStatus.CEASED.value, InfStatus.COMPLETED.value)
GPU_TYPE, VGPU_TYPE = "gpu", "vgpu"
INFERENCE_SERVICE_COUNT = "inference_service_count"

class InfSvcCurd(BaseCURD[InfSvc]):

    @session_manage()
    async def get_svc_usage(self, user_ids: list[str]) -> dict[str, any]:
        """
        获取用户的推理服务用量
        :param user_ids: 用户列表
        :return: 用户推理服务资源用量信息
        """

        user_info_dict = {}
        for user_id in user_ids:
            user_info_dict[user_id] = {
                GPU_TYPE: {},
                VGPU_TYPE: {},
                INFERENCE_SERVICE_COUNT: 0
            }
        sql = """
            SELECT creator, spec_info, cluster_type, instance_num, node_num 
            FROM inference_service 
            WHERE status NOT IN :non_status AND creator IN :user_ids
        """
        rows, _ = await self.query_by_sql(
            sql,
            params={'non_status': NON_STATUS, 'user_ids': tuple(user_ids)}
        )
        for row in rows:
            try:
                creator = row.get("creator")
                if not creator or creator not in user_ids:
                    continue

                user_info_dict[creator][INFERENCE_SERVICE_COUNT] += 1

                spec_info = row.get("spec_info", {})

                aipods_type = spec_info.get("aipods_type", "")
                if aipods_type == "vGPU":
                    resource_type = VGPU_TYPE
                elif aipods_type == "only_gpu":
                    resource_type = GPU_TYPE
                else:
                    continue

                gpu_model = spec_info.get("gpu_model")
                gpu_memory = spec_info.get("gpu_memory")
                if gpu_model is None or gpu_memory is None:
                    continue

                gpu_key = f"{gpu_model} {gpu_memory}"
                total_usage = spec_info.get("gpu_count", 0) * row.get("instance_num", 0) * row.get("node_num", 0)

                user_info_dict[creator][resource_type][gpu_key] = user_info_dict[creator][resource_type].get(gpu_key,0)+ total_usage

            except Exception as e:
                logger.error(f"处理推理服务用量失败: {e}, row: {row}")
        return user_info_dict

    @session_manage()
    async def check_quota(self, user_id: str, product_info, instance_num: int, node_num: int):
        """
        校验用户配额（包括服务数量配额和GPU配额）
        :param user_id: 用户ID
        :param product_info: 产品规格信息
        :param instance_num: 实例数量
        :param node_num: 节点数量
        """
        # 获取用户用量信息
        usage_cnt = (await self.get_svc_usage([user_id])).get(user_id)
        user_rsp = aicp_client.send_request(settings.AICP_ISTIO_SERVER, f'/aicp/user/{user_id}',
                                            auth_type=AuthType.SIGNATURE)
        user_data = user_rsp.get('data', {})
        
        # 校验服务数量配额
        quota = user_data.get('maas_server')
        if quota is not None and usage_cnt.get('inference_service_count', 0) >= quota:
            raise MaaSBaseException(Err.SERVICE_QUOTA_EXCEED, current_usage=usage_cnt.get('inference_service_count', 0), quota=quota)

        # 校验GPU配额
        aipods_type = VGPU_TYPE if product_info.aipods_type == "vGPU" else GPU_TYPE
        model_quotas = "vgpu_model_quotas" if product_info.aipods_type == "vGPU" else "gpu_model_quotas"
        gpu_number = "vgpu_number" if product_info.aipods_type == "vGPU" else "gpu_number"  # 计算请求的GPU资源用量
        request_usage = instance_num * node_num * product_info.gpu_count
        request_gpu = f"{product_info.gpu_model} {product_info.gpu_memory}"
        
        # 按GPU型号配额校验
        if user_data.get(gpu_number) == -1:
            gpu_model_quotas = user_data.get(model_quotas, {}).get(request_gpu)
            current_usage = usage_cnt.get(aipods_type, {}).get(request_gpu, 0)
            if gpu_model_quotas is not None and request_usage + current_usage > gpu_model_quotas:
                raise MaaSBaseException(Err.GPU_MODEL_QUOTA_EXCEED, gpu_model=request_gpu, current_usage=current_usage, quota=gpu_model_quotas)
        # 按总量配额校验
        else:
            current_total = sum(usage for _, usage in usage_cnt.get(aipods_type, {}).items())
            gpu_total_quota = user_data.get(gpu_number)
            if gpu_total_quota is not None and request_usage + current_total > gpu_total_quota:
                raise MaaSBaseException(Err.GPU_TOTAL_QUOTA_EXCEED, current_usage=current_total, quota=gpu_total_quota)

    @session_manage()
    async def create(self, inf_svc_req: CreateInfSvcReq):
        """
        部署推理服务
        """
        user: User = Context.USER.get()
        model_id = inf_svc_req.model_id
        model_info: Model = await model_curd.get_by_id(model_id)

        if not model_info:
            raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=model_id)
        if not inf_svc_req.resource_group:
            if user.user_type == 1 and user.sub_acc_consume != SubAccountCanConsume.YES.value:
                raise MaaSBaseException(Err.AUTH_CONSUME_ERR)
            await BillingCURD.valid_balance(sku_id=inf_svc_req.sku_id,
                                            replica=inf_svc_req.instance_num * inf_svc_req.node_num)
        await model_curd.fill_model_info([model_info])

        # 获取产品规格信息
        if inf_svc_req.resource_group:  # 专属资源池
            rg_info = inf_svc_req.resource_group
            rg_id = rg_info.rg_id

            # 规格模板方式
            if rg_info.tmp_id:
                params = {'pod_type': ['infer'], 'rg_id': rg_id, 'template_ids': [rg_info.tmp_id]}
                if rg_info.spec_type == 'share_gpu':
                    params['is_reused_gpu_node'] = True
                if rg_info.spec_type == 'vGPU':
                    params['is_vgpu_node'] = True
                rsp = aicp_client.send_request(settings.AICP_WEB_SERVER, '/aicp/resource/template', params=params)
                if pydash.get(rsp, 'data[0].resource_group.status') != 'active':
                    raise MaaSBaseException(Err.VALIDATE_PARAMS, message=rg_id)
                spec_info = rsp['data'][0]
                for key, value in spec_info.items():
                    if hasattr(rg_info, key):
                        setattr(rg_info, key, value)
                if rg_info.spec_type == 'share_gpu':
                    rg_info.gpu = rg_info.gpu or len(rg_info.gpu_list)

            # 使用 rg 所有者查询资源池节点的 gpu_model（由于资源池 node gpu 规格都相同，所以查询一个就够了）
            gpu_model = ''
            gpu_memory= 0
            if rg_info.gpu:
                node_rsp = aicp_client.send_request(settings.AICP_WEB_SERVER, '/aicp/resource/node', params={'rg_id': rg_id, 'limit': 1, 'gpu_name': rg_info.gpu_name})
                if not node_rsp['data']:
                    raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f'资源组[{rg_id}]节点')
                if not node_rsp['data'][0]['gpu_model']:
                    raise MaaSBaseException(Err.VALIDATE_PARAMS, message=f'资源组[{rg_id}]节点 GPU 类型不存在')
                gpu_model = node_rsp['data'][0]['gpu_model']
                gpu_memory = node_rsp['data'][0]['gpu_memory']

            product_info = RGProductDTO(**{
                'sku_id': str(rg_info.tmp_id),
                'aipods_scope': 'resource_group',
                'cpu_count': rg_info.cpu,
                'memory': rg_info.memory,
                'gpu_count': rg_info.gpu,
                'rg_node_id': rg_info.rg_node_id,
                'gpu_list': rg_info.gpu_list or [],
                'gpu_model': gpu_model,
                'gpu_memory': rg_info.gpu_memory or gpu_memory,  # 优先使用规格中的 gpu_memory(vGPU，用来限制资源)，否则使用节点的显存（用来展示）
                'spec_type': rg_info.spec_type,
            })
        else:
            # 公共资源池
            product_info = PI.product_interface.get_product_by_id(inf_svc_req.sku_id)
            # 校验配额
            await self.check_quota(user.user_id, product_info, inf_svc_req.instance_num, inf_svc_req.node_num)

        # 设置镜像
        if inf_svc_req.engine_id != DEPLOY_CUSTOM:
            model_engine_dict = pydash.group_by(model_info.inference_engine, 'engine_id')
            if inf_svc_req.engine_id not in model_engine_dict:
                raise MaaSBaseException(Err.VALIDATE_PARAMS, message=f"模型不支持引擎 [{inf_svc_req.engine_id}]")
            model_run_cmd = model_engine_dict[inf_svc_req.engine_id][0].run_cmd
            engine_images = image_curd.engine_img()
            if inf_svc_req.engine_id not in engine_images or not engine_images[inf_svc_req.engine_id].tags:
                raise MaaSBaseException(Err.VALIDATE_PARAMS, message=f"引擎 [{inf_svc_req.engine_id}] 镜像不存在")
            image = ModelImage(**{
                'type': ImageType.OFFICIAL.value,
                'image': engine_images[inf_svc_req.engine_id].tags[0].image
            })
        else:
            if not model_info.image:
                raise MaaSBaseException(Err.VALIDATE_PARAMS, message="模型不支持自定义部署")
            image = ModelImage(**model_info.image)
            model_run_cmd = image.run_cmd

        # 设置默认挂载信息
        mounts = model_info.mounts
        if not mounts:
            mounts = [{
                "host_path": f"{PUBLIC_MODEL_DIR}/{model_info.provider}/{model_info.name}",
                "mount_path": f"/model/{model_info.provider}/{model_info.name}"
            }]

        # 设置运行参数
        run_info = {
            "image": image.model_dump(),
            "mounts": mounts,
            "requirements": model_info.requirements,
            "port": model_info.port,
            "environment": model_info.environment,
            "run_cmd": model_run_cmd,
            "deploy_editable": model_info.deploy_editable,
        }
        if model_info.deploy_editable:
            custom_param = pydash.pick(inf_svc_req.run_info.model_dump(exclude_unset=True), ['requirements', 'port', 'environment', 'run_cmd'])
            run_info.update(custom_param)
            if inf_svc_req.run_info.image:
                run_info['image'].update(inf_svc_req.run_info.image.model_dump(exclude_unset=True))

        service_id = uuid(ResourceModule.INFERENCE_SERVICE, exclude_uppercase=True)
        inf_svc = InfSvc(**{
            "id": service_id,
            "name": inf_svc_req.name or model_info.name,
            "model_id": inf_svc_req.model_id,
            "model": f'{model_info.provider}/{model_info.name}',
            "status": InfStatus.ACTIVE.value,
            "transition_status": InfTransitionStatus.Creating.value,
            "spec_info": product_info.model_dump(exclude_unset=True),
            "api_key": uuid(ResourceModule.API_KEY, length=46) + 'QC',  # 为了和 IPaaS 的令牌区分，末尾使用 QC
            "run_info": run_info,
            "cluster_type": inf_svc_req.cluster_type,
            "engine_id": inf_svc_req.engine_id,
            "instance_num": inf_svc_req.instance_num,
            "node_num": inf_svc_req.node_num,
            "creator": user.user_id,
            "resource_group_id": inf_svc_req.resource_group.rg_id if inf_svc_req.resource_group else ''
        })
        await self.save_one(inf_svc)

        # 创建资源
        kube_opera = OperationManager.get_operation(inf_svc)
        kube_opera.create_namespace()
        kube_opera.create_secret()
        kube_opera.create_inf_svc(product_info, inf_svc_req.resource_group)
        kube_opera.create_service()
        kube_opera.create_virtual_service()
        kube_opera.create_authorization_policy()

        await job_curd.submit_job(resource_id=service_id, action=JobAction.DeployInference.value,
                                  target_replica=inf_svc_req.instance_num, src_replica=0,
                                  resource_transition_status=InfTransitionStatus.Creating.value)
        return service_id

    @staticmethod
    def get_service_event(svc_ids: List[str], namespace) -> dict[str, dict[str, str]]:
        """
        获取多个 svc 每个 pod 的最新 event
        :param svc_ids: svc id 列表
        :param namespace: ns
        :return: {"inf-12345": {"inf-12345-11111-11111": "success"}}
        """
        all_events = kr8s.get('event', namespace=namespace)
        all_events = [e for e in all_events if e["lastTimestamp"]]  # noqa
        all_events = sorted(all_events, key=lambda e: e["lastTimestamp"], reverse=True)
        all_pods = kr8s.get("pod", namespace=namespace, label_selector=f"app in ({','.join(svc_ids)})")
        pod_ids = [v.name for v in all_pods]  # noqa
        pod_event = {}

        for event in all_events:
            if event["involvedObject"]["name"] in pod_ids and event["involvedObject"]["name"] not in pod_event:
                pod_event[event["involvedObject"]["name"]] = EVENT_MAPPING.get(event["reason"], event["message"])

        service_event = {}
        for v in all_pods:  # noqa
            service_event.setdefault(v.labels["app"], {}).update({v.name: pod_event.get(v.name, '')})
        return service_event

    async def fill_svc_info(self, svc_list: List[InfSvc]):
        """
        填充 svc 可用副本数以及 event 数据
        :param svc_list: svc 列表数据
        :return:
        """
        user = Context.USER.get()
        ns_svc_group = pydash.group_by(svc_list, lambda x: x.creator.lower())
        ready_count = {}
        svc_event = {}
        for ns, svcs in ns_svc_group.items():
            svc_ids = pydash.pluck(svcs, 'id')
            pods = kr8s.get("pod", namespace=ns, label_selector=f"app in ({','.join(svc_ids)})")  # noqa
            for pod in pods: # noqa
                svc_id = pod.labels.app
                val = ready_count.setdefault(svc_id, 0)
                if pod.status.phase == 'Running':
                    ready_count[svc_id] = val + 1
            svc_event.update(self.get_service_event(svc_ids, ns))

        for svc in svc_list:
            svc.running_pod_num = ready_count.get(svc.id, 0)
            svc.pod_event = svc_event.get(svc.id, None)

            # 用户端调用，需要根据模型部署是否允许修改，来精简 run_info 中的字段
            if user != KS_ADMIN_USER:
                src_run_info = svc.run_info
                available_fields = ["deploy_editable"]
                if src_run_info.get('deploy_editable'):
                    available_fields.extend(['port', 'image', 'run_cmd', 'environment', 'requirements'])
                svc.run_info = pydash.pick(src_run_info, available_fields)

    @session_manage()
    async def get_svc_pods(self, service_id: str, status: str, sort_key: str, reverse: int):
        inf_svc = await inf_svc_curd.get_by_id(service_id)
        if not inf_svc or inf_svc.status in [InfStatus.CEASED.value, InfStatus.COMPLETED.value]:
            raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{service_id}")

        return OperationManager.get_operation(inf_svc).get_svc_pods(status, sort_key, reverse)


    async def stop(self, service_id, reason='', user=''):

        data = await inf_svc_curd.get_by_id(service_id)
        if not data or data.status not in (InfStatus.ACTIVE.value,):
            raise MaaSBaseException(Err.NOT_FOUND)
        if not data.resource_group_id:
            await BillingCURD.valid_lease_status(service_id, data.creator)

        status = InfStatus.STOP.value if reason != JobReason.ARREARS_SUSPEND.value else InfStatus.SUSPENDED.value
        await self.base_update(service_id, {
            "status": status,
            "transition_status": InfTransitionStatus.STOPPING.value
        }, strict=True)
        await job_curd.submit_job(resource_id=service_id, action=JobAction.StopInference.value, target_replica=0, src_replica=data.instance_num,
                                  resource_transition_status=InfTransitionStatus.STOPPING.value, reason=reason, operator=user, owner=data.creator)
        OperationManager.get_operation(data).scale_inf_svc(0)

    async def start(self, service_id, reason='', user=''):

        data: InfSvc = await self.get_by_id(service_id)
        if not data or data.status not in [InfStatus.SUSPENDED.value, InfStatus.STOP.value]:
            raise MaaSBaseException(Err.NOT_FOUND)

        if not data.resource_group_id:
            await BillingCURD.valid_balance(sku_id=data.spec_info.get('sku_id'), replica=data.instance_num * data.node_num,
                                            resource_id=service_id if data.status != InfStatus.SUSPENDED else DEF_RES_ID, user_id=data.creator)
        await self.base_update(service_id, {"status": InfStatus.ACTIVE.value,
                                            "transition_status": InfTransitionStatus.STARTING.value}, strict=True)
        await job_curd.submit_job(resource_id=service_id, action=JobAction.StartInference.value, target_replica=data.instance_num, src_replica=0,
                                  resource_transition_status=InfTransitionStatus.STARTING.value, reason=reason, operator=user, owner=data.creator)
        OperationManager.get_operation(data).scale_inf_svc(data.instance_num)

    async def delete(self, service_id: str, user=None, reason=""):

        data = await self.get_by_id(service_id)
        if not data or data.status in DELETED_STATUS:
            raise MaaSBaseException(Err.NOT_FOUND)
        owner = data.creator
        columns = {
            "transition_status": InfTransitionStatus.DELETING.value,
            "status": InfStatus.CEASED.value,
            "instance_num": 0
        }
        await self.base_update(service_id, columns, strict=True)
        await job_curd.submit_job(resource_id=service_id, action=JobAction.DeleteInference.value, target_replica=0, src_replica=data.instance_num,
                                  reason=reason, resource_transition_status=InfTransitionStatus.DELETING.value, operator=user, owner=owner)

        OperationManager.get_operation(data).delete()
        user = PI.user_interface.get_user_by_id(owner)
        push_client.push_msg_to_user("release_resource", owner, {
            "username": user.user_name,
            "zone": settings.QINGCLOUD_ZONE,
            "resource": "推理服务",
            "id": service_id,
            "name": data.name
        })

    async def scale(self, service_id: str, instance_num: int, user=None, reason=''):
        data = await self.get_by_id(service_id)
        if not data or data.status != InfStatus.ACTIVE.value:
            raise MaaSBaseException(Err.NOT_FOUND)

        if not data.resource_group_id:
            await BillingCURD.valid_balance(sku_id=data.spec_info.get('sku_id'), replica=instance_num * data.node_num, resource_id=service_id, user_id=data.creator)

        columns = {"transition_status": InfTransitionStatus.UPDATING.value, "instance_num": instance_num}
        await self.base_update(service_id, columns, strict=True)
        if data.instance_num > instance_num:
            action = JobAction.DecreasePod.value
        else:
            action = JobAction.IncreasePod.value
        await job_curd.submit_job(resource_id=service_id, action=action, target_replica=instance_num, src_replica=data.instance_num,
                                  resource_transition_status=InfTransitionStatus.UPDATING.value, reason=reason, operator=user, owner=data.creator)
        OperationManager.get_operation(data).scale_inf_svc(instance_num)

    async def get_log(self, service_id: str, instance_id: Optional[str] = None, key_word: Optional[str] = None,
                     start_time: Optional[datetime] = None, end_time: Optional[datetime] = None,
                     namespace: Optional[str] = None, size: int = 10000, page: int = 0, reverse: bool = False,
                     stream: bool = False):
        """
        获取推理服务日志
        :param service_id: 服务ID
        :param instance_id: 实例ID
        :param key_word: 关键词
        :param start_time: 开始时间（UTC时间）
        :param end_time: 结束时间（UTC时间）
        :param namespace: 命名空间
        :param size: 每页大小
        :param page: 页码
        :param reverse: 是否倒序
        :param stream: 是否以流式格式返回
        :return: 如果 stream=True 返回格式化的日志字符串，否则返回 (logs, total)
        """
        # 检查服务是否存在
        data = await self.get_by_id(service_id)
        if not data:
            raise MaaSBaseException(Err.NOT_FOUND)
        # 获取pod列表
        if instance_id:
            pod_ids = [instance_id]
        else:
            pod_info = opensearch_client.get_all_pod(service_id, namespace or data.creator.lower())
            pod_ids = [v["metadata"]["name"] for v in pod_info]
            if not pod_ids:
                pods = kr8s.get("pods", namespace=namespace or data.creator.lower(), label_selector=f"app={service_id}")
                pod_ids = [v.name for v in pods]  # noqa

        if not pod_ids:
            raise MaaSBaseException(Err.NOT_FOUND)

        # 获取日志
        return opensearch_client.search_pod_log(
            pod_id=pod_ids,
            key_word=key_word,
            start_time=start_time,
            end_time=end_time,
            size=size,
            page=page,
            reverse=reverse,
            stream=stream
        )

    async def rebuild_pod(self, service: InfSvc, pod_id):
        namespace = service.creator.lower()
        columns = {
            "transition_status": InfTransitionStatus.UPDATING.value,
        }
        await self.base_update(service.id, columns, strict=True)
        await job_curd.submit_job(resource_id=service.id, action=JobAction.RebuildPod.value,
                                  target_replica=service.instance_num, src_replica=service.instance_num,
                                  resource_transition_status=InfTransitionStatus.UPDATING.value, owner=service.creator)
        pod = Pod.get(pod_id, namespace=namespace)  # noqa
        pod.delete()

    @cached(cache=TTLCache(maxsize=1024, ttl=3600 * 24))
    async def get_service_api_key(self, service_id: str) -> str:
        """
        查询推理服务对应的令牌
        :param service_id: 推理服务id
        :return: 令牌
        """
        rows, _ = await self.query_by_sql('select api_key from inference_service where id = :id', params={"id": service_id})
        return rows[0].get('api_key') or '' if rows else ''


inf_svc_curd = InfSvcCurd()
