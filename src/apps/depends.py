# -*- coding: utf-8 -*-

from typing import Optional

import pydash
from fastapi import Request

from src.apps.base_curd import base_curd
from src.common.const.comm_const import UserPlat, SubAccountCanConsume, AuthType
from src.common.const.err_const import Err
from src.common.context import Context
from src.common.dto import ValidOwner, User, Permissions, KS_ADMIN_USER
from src.common.exceptions import MaaSBaseException
from src.common.loggers import logger


def check_permission(plat: UserPlat = None, valid_owner: Optional[ValidOwner] = None, valid_consume: bool = False):
    """
    权限拦截器
    :param plat: 平台（云平台或者ks平台）
    :param valid_owner: 校验资源所有者
    :param valid_consume: 校验消费权限
    :return:
    """

    async def validate_permission(request: Request):
        user: User = Context.USER.get()

        # 平台权限不足
        if plat and user.plat not in plat.value.split(","):
            raise MaaSBaseException(Err.AUTH_PLAT_ERR, plat=plat.value)

        # 消费权限不足
        if valid_consume and user.user_type == 1 and user.sub_acc_consume != SubAccountCanConsume.YES.value:
            raise MaaSBaseException(Err.AUTH_CONSUME_ERR)

        # 校验资源所有者权限
        if valid_owner and user != KS_ADMIN_USER:

            # 获取参数中资源 id
            id_val = None
            for id_field in valid_owner.biz_id_field:
                id_val = request.path_params.get(id_field) or request.query_params.get(id_field)
                if not id_val:
                    data = await request.json() or {}
                    id_val = data.get(id_field)
                if id_val:
                    break
            if not id_val:
                raise MaaSBaseException(Err.REQUIRE_PARAMS, fields=f"{valid_owner.biz_id_field}")

            # 查询资源所有者
            sql_str = f"select {valid_owner.tb_user_field} user_id from {valid_owner.tb_name} where {valid_owner.tb_id_field} = :id_val"
            data_ret = await base_curd.query_by_sql(sql_str, {"id_val": id_val})
            if not data_ret or not data_ret[0] or not data_ret[0][0]:
                raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields=f"{id_val}")
            owner = data_ret[0][0].get('user_id')
            user.owner = owner

            # ------------------------ 校验权限 ----------------------
            sub_user_ids = [sub_user.id for sub_user in user.sub_users]

            # 其他账号的资源（不相干）
            if owner not in sub_user_ids and owner != user.root_user_id:
                raise MaaSBaseException(Err.AUTH_OWNER_ERR)

            # 主账号拥有自己和子账号所有权限，子账号拥有自己数据的权限
            if user.user_type == 0 or owner == user.user_id:
                return True

            # 校验读写权限，这里都是子账号操作其他账号的逻辑
            permission: Permissions = pydash.find(user.permissions, lambda x: x.module == 'INF')
            if permission.permission == 'READALL' and valid_owner.auth_type == AuthType.READ:
                return True
            if permission.permission == 'ALL':
                return True
            logger.warning(f'[权限] 用户[{user.user_id}] 权限[{permission.permission}] 无权调用接口[{request.method} {request.url.path}], 资源[{id_val}] 所有者[{owner}]')
            raise MaaSBaseException(Err.AUTH_OWNER_ERR)

        return True

    return validate_permission
