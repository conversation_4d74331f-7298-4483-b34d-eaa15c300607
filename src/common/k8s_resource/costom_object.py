from kr8s._io import sync
from kr8s._objects import APIObject
from kr8s.objects import new_class

VirtualServiceObject = sync(new_class(
    kind="VirtualService",
    version="networking.istio.io/v1alpha3",
    namespaced=True,
    asyncio=False,
))

auth_policy = "AuthorizationPolicy"
AuthorizationPolicyObject = sync(type(auth_policy, (APIObject,), {
    "kind": "AuthorizationPolicy",
    "version": "security.istio.io/v1beta1",
    "_asyncio": False,
    "endpoint": "authorizationpolicies",
    "plural": "authorizationpolicies",
    "singular": auth_policy.lower(),
    "namespaced": True,
    "scalable": False,
    "scalable_spec": "replicas",
}))

lws = "LeaderWorkerSet"
LeaderWorkerSetObject = sync(type(lws, (APIObject,), {
    "kind": "LeaderWorkerSet",
    "version": "leaderworkerset.x-k8s.io/v1",
    "_asyncio": False,
    "endpoint": "leaderworkersets",
    "plural": "leaderworkersets",
    "singular": lws.lower(),
    "namespaced": True,
    "scalable": True,
    "scalable_spec": "replicas",
}))