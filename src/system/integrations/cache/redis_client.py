# -*- coding: utf-8 -*-
import redis

from src.common.loggers import logger
from src.setting import settings
from typing import List, Dict, Any, Optional
import asyncio
from datetime import datetime

class RedisClient:

    def __init__(self, prefix):
        self.prefix = prefix
        self.conn = redis.StrictRedis(host=settings.REDIS_HOST, port=settings.REDIS_PORT, decode_responses=True)

    def set(self, key, value, nx=False, ex=60):
        return self.conn.set(f"{self.prefix}{key}", value, nx=nx, ex=ex)

    def get(self, key):
        return self.conn.get(f"{self.prefix}{key}")

    def delete(self,  key):
        return self.conn.delete(f"{self.prefix}{key}")

    def mset(self, data: dict, ex=60):
        if self.conn.mset(data):
            for key in data.keys():
                self.conn.expire(key, ex)

    def mget(self, keys):
        return self.conn.mget(keys)

    def hset(self, name, mappings, ex=60):
        if self.conn.hset(f"{self.prefix}{name}", mapping=mappings):
            self.conn.expire(f"{self.prefix}{name}", ex)

    def hget(self, name, key):
        return self.conn.hget(f"{self.prefix}{name}", key)

    def hget_all(self, name):
        return self.conn.hgetall(f"{self.prefix}{name}")

    def hdel(self, name, key):
        return self.conn.hdel(f"{self.prefix}{name}", key)

    def expire(self, key, ex):
        return self.conn.expire(f"{self.prefix}{key}", ex)

    def incr_exp(self, key, exp=60):
        if self.conn.exists(f"{self.prefix}{key}"):
            self.conn.incr(f"{self.prefix}{key}")
        else:
            self.conn.incr(f"{self.prefix}{key}")
            self.conn.expire(f"{self.prefix}{key}", exp)

    def product_msg(self, queue, data, max_len=settings.EVENT_QUEUE_MAX_LEN):
        return self.conn.xadd(f'{self.prefix}{queue}', data, maxlen=max_len)

    def consume_msg(self, queue, group_name=None, consumer_name=None, count=50, block=10000):
        queue = f"{self.prefix}{queue}"
        message = self.conn.xreadgroup(group_name, consumer_name, {queue: '>'}, count=count, block=block)

        # 如果消息全部消费，检查 pending 消息
        if not message:
            pending_info = self.conn.xpending(queue, group_name)
            min_id = pending_info["min"]
            max_id = pending_info["max"]
            if min_id is None or max_id is None:
                return []
            message = self.conn.xrange(queue, min_id, max_id, count=count)
            logger.warning(f'消费 pending 状态信息: {message}')
            return message
        return message[0][1]

    def ack_msg(self, queue, group_name, message_ids, need_del=True):
        queue = f'{self.prefix}{queue}'
        self.conn.xack(queue, group_name, *message_ids)
        if need_del:
            self.conn.xdel(queue, *message_ids)

    def init_consume_group(self, queue, group_name):
        queue = f'{self.prefix}{queue}'
        try:
            self.conn.xgroup_create(queue, group_name, id='0', mkstream=True)
        except redis.exceptions.ResponseError as e:
            if not 'BUSYGROUP Consumer Group name already exists' in str(e):
                logger.error(f'创建消费者组[{group_name}]失败', e)
                raise e

redis_client = RedisClient('maas:')
redis_operation_client = RedisClient('operation:')


def create_redis_data(user_id: str, operation_user_id: str, resource_id: str, resource_type: str, operation: str,
                      start_time: datetime, trace_id: str):

    return {
        "user_id": user_id,
        "operation_user_id": operation_user_id,
        "resource_id": resource_id,
        "resource_type": resource_type,
        "operation": operation,
        "start_time": start_time,
        "trace_id": trace_id,
    }


async def producer(data: Dict):
    try:
        message_id = await redis_operation_client.product_msg(settings.OPERATION_INVOKE_EVENT_QUEUE, data)
        logger.info(f"Produced message ID: {message_id}, Data: {data}")
    except Exception as e:
        logger.info(f"Error producing message: {e}")
    await asyncio.sleep(1)