# -*- coding: utf-8 -*-

import asyncio
from datetime import datetime

import kr8s
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from kr8s.objects import Job

from src.apps import job_curd, model_curd
from src.apps.billing.curd import BillingCURD
from src.apps.inference_service.curd import inf_svc_curd
from src.apps.inference_service.rsp_schema import InfSvc
from src.common.const.comm_const import JobStatus, JobAction, InfStatus, AlertReason, ResourceModule, ModelStatus
from src.common.exceptions import InterfaceException
from src.common.k8s_resource.base_operation import OperationManager
from src.common.loggers import task_logger as logger
from src.jobs.common import set_job_success, set_job_failed
from src.jobs.global_job import global_task
from src.setting import settings
from src.system.integrations.push.push_client import push_client


@global_task('全局定时任务')
def global_cron_job():

    # 创建一个新的事件循环，用来执行定时任务
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    scheduler = AsyncIOScheduler()

    # job 超时
    scheduler.add_job(monitor_job, 'interval', seconds=settings.MONITOR_JOB_INTERVAL,
                      next_run_time=datetime.now(), misfire_grace_time=10, max_instances=1)

    # 对账
    scheduler.add_job(inf_audit, 'interval', seconds=settings.AUDIT_JOB_INTERVAL,
                      next_run_time=datetime.now(), misfire_grace_time=10, max_instances=1)
    scheduler.start()

    if not asyncio.get_event_loop().is_running():
        asyncio.get_event_loop().run_forever()


async def monitor_job():
    """ 检测 job 完成或超时"""

    running_jobs = await job_curd.get_by_filter([{'column_name': 'status', 'value': JobStatus.RUNNING.value}])
    if not running_jobs:
        return

    for job in running_jobs:
        try:
            # job 运行时长
            exec_duration = (datetime.now() - job.create_time).seconds
            if job.resource_id.startswith(ResourceModule.INFERENCE_SERVICE.value):
                logger.info(f'推理服务任务[{job.job_id}/{job.resource_id}][{job.action}]检测中...')
                # 查询服务状态
                infer_service = await inf_svc_curd.get_by_id(job.resource_id)
                namespace = infer_service.creator.lower()
                if job.action == JobAction.DeleteInference.value:
                    available_replicas = 0
                else:
                    pod_info = OperationManager.get_operation(infer_service).get_svc_pods()
                    available_replicas = pod_info.running_pod_num

                # 检测任务是否完成
                if available_replicas == job.target_replica * infer_service.node_num:
                    await set_job_success(job)
                    logger.info(f'任务[{job.job_id} {job.resource_id}][{job.action}]完成 目标数: {job.target_replica}')
                    continue

                # 检测是否超时
                if exec_duration < settings.JOB_TIMEOUT.get(job.action, 600):
                    continue

                event_reason = ''
                event_msg = ''
                pods = kr8s.get('pod', namespace=namespace, label_selector=f'app={job.resource_id}')
                for pod in pods:  # noqa
                    event = kr8s.get('events', namespace=namespace, field_selector=f'involvedObject.name={pod.name}')
                    event = [e for e in event if e["eventTime"] and e['type'] != 'Normal' and e['reason'] not in ('Unhealthy',)]
                    event = sorted(event, key=lambda e: e['eventTime'], reverse=True)
                    if event:
                        event_reason, event_msg = event[0]["reason"], event[0]["message"]
                        break
                real_replica = min(available_replicas, job.target_replica * infer_service.node_num)
                logger.error(f"任务[{job.job_id}][{job.resource_id}][{job.action}] 超时: {event_reason} {event_msg}")
                await set_job_failed(job, real_replica, reason=f'任务超时 {event_reason}', msg=event_msg)

                # 创建->删除;启动->关闭;扩容->回退副本数;关闭,删除,重建,缩容->不用管;
                if job.action == JobAction.DeployInference.value:
                    await inf_svc_curd.delete(job.resource_id, user=job.creator, reason=f'{job.job_id} 创建超时')
                elif job.action == JobAction.StartInference.value:
                    await inf_svc_curd.stop(job.resource_id, user=job.creator, reason=f'{job.job_id} 启动超时')
                elif job.action == JobAction.IncreasePod.value:
                    await inf_svc_curd.scale(job.resource_id, job.src_replica, user=job.creator, reason=f'{job.job_id} 扩容超时')
            elif job.resource_id.startswith(ResourceModule.MODEL.value):
                if job.action == JobAction.ModelDownload.value and exec_duration > settings.JOB_TIMEOUT.get(job.action, 600):
                    await job_curd.base_update(job.job_id, {'status': JobStatus.FAILED.value, 'reason': '任务超时'})
                    await model_curd.base_update(job.resource_id, {'status': ModelStatus.DOWNLOAD_FAILED.value})
                    Job.get(job.resource_id, namespace=job.creator.lower()).delete()  # noqa
                    logger.error(f'任务[{job.job_id}] 超时,下载模型[{job.remark}]失败')

        except Exception:  # noqa
            logger.exception(f'任务[{job.job_id}]检测失败: ')


async def inf_audit():
    """
    审计（对账）
    1. db 和 k8s 资源
    2. k8s 资源和计费
    :return:
    """

    logger.info('开始对账...')
    active_service: list[InfSvc] = await inf_svc_curd.get_by_filter([
        {"column_name": "status", "operator": "ne", "value": InfStatus.COMPLETED.value},
        {"column_name": "resource_group_id", "value": ""},
        {"column_name": "transition_status", "value": ""},
    ])
    if not active_service:
        logger.info('对账完成')
        return

    for service in active_service:
        service_id, owner = service.id, service.creator
        try:
            # 1. db 中服务状态和 k8s 实际运行状态对比
            if service.status == InfStatus.CEASED.value:
                available_replicas = 0
            else:
                pod_info = OperationManager.get_operation(service).get_svc_pods()
                available_replicas = pod_info.running_pod_num

            if ((service.status in (InfStatus.STOP.value, InfStatus.CEASED.value, InfStatus.SUSPENDED.value) and available_replicas != 0) or
                    (service.status in (InfStatus.ACTIVE.value,) and available_replicas != service.instance_num * service.node_num)):
                msg = f"推理服务 {service_id}, 状态 {service.status}, 实际实例数 {available_replicas}, 与预期实例数 {service.instance_num * service.node_num} 不一致"
                logger.error(msg)
                push_client.send_maas_alert_event(AlertReason.MaaSCheckAccountsError, message=msg)
                continue

            # 2. db 中服务状态和 billing 合同对比
            if not settings.BILLING_ENABLE:
                continue

            is_success, msg = await billing_audit(service_id, owner, service.status, available_replicas)
            if not is_success:
                logger.error(msg)
                push_client.send_maas_alert_event(AlertReason.MaaSCheckAccountsError, message=msg)
            elif service.status == InfStatus.CEASED.value:
                await inf_svc_curd.base_update(service_id, {'status': InfStatus.COMPLETED.value})
        except Exception:  # noqa
            logger.exception(f'对账失败[{service.id}]: ')
    logger.info('对账完成')


BILLING_AUDIT_CONDITION = {
    InfStatus.ACTIVE.value: ('active', lambda real_replica, lease_replica: real_replica == lease_replica),
    InfStatus.STOP.value: ('active', lambda real_replica, lease_replica: lease_replica == 0),
    InfStatus.SUSPENDED.value: ('suspended', None),
    InfStatus.CEASED.value: ('ceased', None),
}

async def billing_audit(service_id, owner, service_status, real_replica):
    try:
        lease_info = await BillingCURD.get_lease_info(resource_id=service_id, user_id=owner)
        lease_status = lease_info.status
        lease_replica = lease_info.contract.price_info.replicas
    except Exception as e:
        if service_status == InfStatus.CEASED.value and isinstance(e, InterfaceException) and "ResourceNotFound" in e.prams["message"]:
            return True, ''
        logger.exception(f"查询推理服务{service_id} 租赁信息失败:")
        return False, f"查询推理服务 {service_id} 租赁信息失败"

    if service_status in BILLING_AUDIT_CONDITION:
        (billing_status, condition) = BILLING_AUDIT_CONDITION[service_status]
        if lease_status != billing_status or (condition and not condition(real_replica, lease_replica)):
            return False, f'推理服务 {service_id} 状态 {service_status}, 实际副本数 {real_replica}。 计费状态 {lease_status}, 计费副本数 {lease_replica}。'
    else:
        return False, f'非预期的服务状态[{service_status}]'

    return True, ''
