# -*- coding: utf-8 -*-
import os
import unittest

os.environ['ENV_CONF'] = 'dev'
from src.system.interface import PI


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self) -> None:
        ...

    async def test_get_product_by_id(self):
        ret = PI.product_interface.get_product_by_id("sku_Q07kzLoPjz6p")
        print(ret.json())


if __name__ == '__main__':
    unittest.main()
