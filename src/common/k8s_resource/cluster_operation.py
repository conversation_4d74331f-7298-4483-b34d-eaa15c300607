from kr8s.objects import Service

from src.apps.inference_service.rsp_schema import InfSvc
from src.common.k8s_resource.base_operation import INF_CONTAINER_NAME, KubeOperation
from src.common.k8s_resource.costom_object import LeaderWorkerSetObject, AuthorizationPolicyObject, VirtualServiceObject
from src.common.k8s_resource.k8s_helper import create_k8s_res
from src.common.k8s_resource.spec import AbsGPUStrategy


class ClusterKubeOperation(KubeOperation):

    def __init__(self, inf_svc: InfSvc):
        super().__init__(inf_svc)
        self.svc_name = self.service_id + '-leader'

    def create_service(self):
        template = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": self.svc_name,
                "namespace": self.namespace,
                "labels": {"maas": "inference"}
            },
            "spec": {
                "selector": {"app": self.service_id, "leaderworkerset.sigs.k8s.io/name": self.service_id, "role": "leader"},
                "ports": [
                    {"name": "inf-server", "protocol": "TCP", "port": 80, "targetPort": int(self.port)},
                    {"name": "istio-metrics", "protocol": "TCP", "port": 15020, "targetPort": 15020}
                ]
            }
        }
        create_k8s_res(Service, template)

    def do_create_inf_svc(self, gpu_strategy: AbsGPUStrategy, command: str, start_cmd: str, vols: list, mounts: list, env_dict_list: list):
        # 参考 https://github.com/kubernetes-sigs/lws/blob/main/docs/examples/vllm/GPU/lws.yaml
        # https://docs.vllm.ai/en/latest/deployment/frameworks/lws.html
        # leader 和 worker pod 名字都使用 INF_CONTAINER_NAME

        # python3 -m sglang.launch_server --model-path {model_path} --served-model-name {model_name} --tp {total_gpu_counts}
        # --dist-init-addr $LWS_LEADER_ADDRESS:5000 --nnodes $LWS_GROUP_SIZE --node-rank $LWS_WORKER_INDEX --trust-remote-code
        # --host 0.0.0.0 --port {server_port} --show-time-cost --enable-metrics --context-length {max_model_len}

        # python3 -m sglang.launch_server --model-path {ENV_MODEL_PATH} --served-model-name {ENV_MODEL_NAME} --tp {ENV_INSTANCE_GPU_COUNT} --dist-init-addr $LWS_LEADER_ADDRESS:5000
        # --nnodes $LWS_GROUP_SIZE --node-rank $LWS_WORKER_INDEX --trust-remote-code --host 0.0.0.0 --port {ENV_SERVER_PORT} --show-time-cost --enable-metrics --context-length 32768

        leader_command, worker_command = start_cmd, start_cmd
        if self.inf_svc.engine_id == 'vLLM':
            leader_command = "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh leader --ray_cluster_size=$(LWS_GROUP_SIZE); " + leader_command
            worker_command = "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh worker --ray_address=$(LWS_LEADER_ADDRESS)"

        nccl_env = {
            "GLOO_SOCKET_IFNAME": "eth0",
            "NCCL_IB_HCA": "mlx5",
            "NCCL_P2P_LEVEL": "NVL",
            "NCCL_IB_GID_INDEX": 3,
            "NCCL_IB_CUDA_SUPPORT": 1,
            "NCCL_IB_DISABLE": 0,
            "NCCL_SOCKET_IFNAME": "eth0",
            "NCCL_DEBUG": "INFO",
            "NCCL_NET_GDR_LEVEL": 2,
            "NCCL_IB_TC": 160,
            "NCCL_IB_QPS_PER_CONNECTION": 2
        }
        env_dict_list.extend([{"name": key, "value": str(val)} for key, val in nccl_env.items()])

        template = {
            'apiVersion': 'leaderworkerset.x-k8s.io/v1',
            'kind': 'LeaderWorkerSet',
            'metadata': {
                'name': self.service_id,
                'namespace': self.namespace,
                'labels': {'maas': 'inference'}
            },
            'spec': {
                'replicas': self.inf_svc.instance_num,
                'startupPolicy': 'LeaderCreated',
                'rolloutStrategy': {
                    'type': 'RollingUpdate',
                    'rollingUpdateConfiguration': {'maxSurge': 0, 'maxUnavailable': self.inf_svc.instance_num * self.inf_svc.node_num}
                },
                "leaderWorkerTemplate": {
                    "size": self.inf_svc.node_num,
                    "restartPolicy": "RecreateGroupOnPodRestart",
                    "leaderTemplate": {
                        "metadata": {
                            "labels": {"role": "leader", "app": self.service_id, "maas": "inference"},
                            "annotations": {
                                "traffic.sidecar.istio.io/includeInboundPorts": f"{self.port}",
                                "traffic.sidecar.istio.io/includeOutboundIPRanges": ""
                            }
                        },
                        "spec": {
                            "containers": [{
                                "name": INF_CONTAINER_NAME,
                                "image": self.image.image,
                                "imagePullPolicy": "IfNotPresent",
                                "command": command,
                                "args": [leader_command],
                                "env": env_dict_list,
                                "tty": True,
                                "ports": [
                                    {"containerPort": self.port, "name": "http", "protocol": "TCP"},
                                    {"containerPort": 5000, "name": "distributed", "protocol": "TCP"},
                                ],
                                "resources": {'requests': gpu_strategy.requests, 'limits': gpu_strategy.limits},
                                "securityContext": {"capabilities": {"add": ["IPC_LOCK", "SYS_PTRACE"]}},
                                "volumeMounts": mounts,
                                "readinessProbe": {
                                    "tcpSocket": {"port": self.port},
                                    "initialDelaySeconds": 5,
                                    "periodSeconds": 5,
                                    "failureThreshold": 200
                                },
                                "livenessProbe": {
                                    "tcpSocket": {"port": self.port},
                                    "initialDelaySeconds": 15,
                                    "periodSeconds": 10,
                                    "failureThreshold": 100
                                }
                            }],
                            "volumes": vols,
                            "schedulerName": gpu_strategy.scheduler,
                            "nodeSelector": gpu_strategy.node_selector,
                            "tolerations": gpu_strategy.tolerations,
                            "affinity": gpu_strategy.affinity,
                        }
                    },
                    "workerTemplate": {
                        "metadata": {
                            "labels": {"role": "worker", "app": self.service_id, "maas": "inference"},
                            "annotations": {
                                "traffic.sidecar.istio.io/includeInboundPorts": f"{self.port}",
                                "traffic.sidecar.istio.io/includeOutboundIPRanges": ""
                            }
                        },
                        "spec": {
                            "containers": [{
                                "name": INF_CONTAINER_NAME,
                                "image": self.image.image,
                                "imagePullPolicy": "IfNotPresent",
                                "command": command,
                                "args": [worker_command],
                                "env": [
                                    *env_dict_list,
                                    {"name": "LWS_WORKER_INDEX", "valueFrom": {"fieldRef": {"fieldPath": "metadata.labels['leaderworkerset.sigs.k8s.io/worker-index']"}}}
                                ],
                                "tty": True,
                                "ports": [
                                    {"containerPort": self.port, "name": "http", "protocol": "TCP"},
                                    {"containerPort": 5000, "name": "distributed", "protocol": "TCP"},
                                ],
                                "resources": {'requests': gpu_strategy.requests, 'limits': gpu_strategy.limits},
                                "securityContext": {"capabilities": {"add": ["IPC_LOCK", "SYS_PTRACE"]}},
                                "volumeMounts": mounts,
                            }],
                            "volumes": vols,
                            "schedulerName": gpu_strategy.scheduler,
                            "nodeSelector": gpu_strategy.node_selector,
                            "tolerations": gpu_strategy.tolerations,
                            "affinity": gpu_strategy.affinity,
                        }
                    }
                }
            }
        }

        if self.secret_name:
            template["spec"]["leaderWorkerTemplate"]["leaderTemplate"]["spec"]["imagePullSecrets"] = [{"name": self.secret_name}]
            template["spec"]["leaderWorkerTemplate"]["workerTemplate"]["spec"]["imagePullSecrets"] = [{"name": self.secret_name}]
        create_k8s_res(LeaderWorkerSetObject, template)

    def scale_inf_svc(self, target_replicas: int):
        """
        推理服务扩缩容
        :param target_replicas: 目标副本
        :return:
        """
        lws = LeaderWorkerSetObject.get(self.service_id, namespace=self.namespace)  # noqa
        lws.scale(target_replicas)

    def delete(self):
        Service.get(self.service_id, namespace=self.namespace).delete()  # noqa
        Service.get(self.svc_name, namespace=self.namespace).delete()  # noqa
        AuthorizationPolicyObject.get(self.service_id, namespace=self.namespace).delete()
        VirtualServiceObject.get(self.service_id, namespace=self.namespace).delete()
        LeaderWorkerSetObject.get(self.service_id, namespace=self.namespace).delete()  # noqa
