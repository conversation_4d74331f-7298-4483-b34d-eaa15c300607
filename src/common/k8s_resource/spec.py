# -*- coding: utf-8 -*-
"""
产品规格相关
"""
import re
from abc import ABC
from dataclasses import dataclass, field
from typing import Pattern, Any

from src.apps.billing.rsp_schema import ProductDTO, RGProductDTO
from src.apps.inference_service.req_schema import ResourceGroup
from src.apps.inference_service.rsp_schema import InfSvc
from src.common.const.comm_const import InfSvcClusterType
from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException


@dataclass
class GPUSpecItem:
    node_selector: dict[str, Any] = field(default_factory=dict)
    resource: dict[str, Any] = field(default_factory=dict)
    vgpu_resource: dict[str, Any] = field(default_factory=dict)
    vgpu_aipods: str = ''
    affinity: dict[str, Any]|None = field(default_factory=dict)
    ext_mount: list = field(default_factory=list)
    ext_vol: list = field(default_factory=list)
    tolerations: list = field(default_factory=list)
    ext_env: list = field(default_factory=list)
    res_req: dict[str, Any] = field(default_factory=dict)


GPU_SPECS = {}


def spec_matcher(gpu_pattern: Pattern[str]):
    def decorator(cls):
        GPU_SPECS[gpu_pattern] = cls
        return cls

    return decorator


class AbsGPUStrategy(ABC):

    def __init__(self, prod_info: ProductDTO, gpu_spec_item: GPUSpecItem, inf_svc: InfSvc):
        self.prod_info = prod_info
        self.inf_svc = inf_svc
        self.node_selector = {}
        self.affinity = {}
        self.gpu_spec_item = gpu_spec_item
        self.mount = []
        self.vol = []
        self.scheduler = 'hami-scheduler' if self.is_vgpu() else 'default-scheduler'
        self.ext_env = gpu_spec_item.ext_env

        # 资源规格
        self.requests = gpu_spec_item.res_req or {'memory': f'{prod_info.memory}Gi', 'cpu': prod_info.cpu_count}
        self.limits = {'memory': f'{prod_info.memory}Gi', 'cpu': prod_info.cpu_count}
        resource = gpu_spec_item.vgpu_resource if self.is_vgpu() else gpu_spec_item.resource
        self.requests.update(resource)
        self.limits.update(resource)

        # node selector
        if prod_info.aipods_type:
            self.node_selector['aicp.group/aipods_type'] = prod_info.aipods_type
        self.node_selector.update(gpu_spec_item.node_selector)

        # affinity
        if gpu_spec_item.affinity is not None:
            self.affinity.update(gpu_spec_item.affinity)
            if not self.affinity and not self.is_vgpu():
                self.affinity['podAffinity'] = {
                    "preferredDuringSchedulingIgnoredDuringExecution": [{
                        "weight": 50,
                        "podAffinityTerm": {
                            "labelSelector": {
                                "matchExpressions": [{
                                    "key": "aicp.group/workload",
                                    "operator": "In",
                                    "values": ["container"]
                                }]
                            },
                            "topologyKey": "kubernetes.io/hostname"
                        }
                    }]
                }

        # volume
        if prod_info.gpu_count:
            rate = 10 if self.inf_svc.cluster_type == InfSvcClusterType.DISTRIBUTE else 1
            self.vol.append({
                "name": "shared-memory-devshm",
                "emptyDir": {"medium": "Memory", "sizeLimit": f"{prod_info.gpu_count * rate}Gi"}
            })
            self.mount.append({"mountPath": "/dev/shm", "name": "shared-memory-devshm"})
        self.mount.extend(gpu_spec_item.ext_mount)
        self.vol.extend(gpu_spec_item.ext_vol)

        # tolerations
        self.tolerations = [{'key': 'aicp.group/worker', 'operator': 'Exists'}]
        self.tolerations.extend(gpu_spec_item.tolerations)


    def is_vgpu(self) -> bool:
        return self.prod_info.aipods_type and self.prod_info.aipods_type == self.gpu_spec_item.vgpu_aipods


@spec_matcher(re.compile('^(NVIDIA|Tesla|Quadro).*$'))
class NvidiaGPUStrategy(AbsGPUStrategy):

    def __init__(self, prod_info: ProductDTO, inf_svc: InfSvc):
        resource = {'nvidia.com/gpu': prod_info.gpu_count}
        if prod_info.network:
            resource['nvidia.com/hostdev'] = prod_info.network
        super().__init__(prod_info, GPUSpecItem(**{
            'vgpu_aipods': 'vGPU',
            'node_selector': {'nvidia.com/gpu.product': prod_info.gpu_model},
            'resource': resource,
            'vgpu_resource': {
                'qingcloud.nvidia.com/vgpu': prod_info.gpu_count,
                'qingcloud.nvidia.com/vgpumem': prod_info.gpu_memory * 1000
            }
        }), inf_svc)


@spec_matcher(re.compile('^Ascend.*$'))
class AscendGPUStrategy(AbsGPUStrategy):

    def __init__(self, prod_info: ProductDTO, inf_svc: InfSvc):
        super().__init__(prod_info, GPUSpecItem(**{
            'vgpu_aipods': 'adapter',
            'node_selector': {'servertype': prod_info.gpu_model},
            'resource': {'huawei.com/Ascend910': prod_info.gpu_count},
            'vgpu_resource': {'qingcloud.adapter.com/adapter': prod_info.gpu_memory},
            'ext_mount': [{
                "mountPath": "/usr/local/Ascend/driver",
                "name": "ascend-driver",
                "readOnly": True
            }, {
                "mountPath": "usr/local/Ascend/add-ons",
                "name": "ascend-add-ons",
                "readOnly": True
            }, {
                "mountPath": "/usr/local/sbin/npu-smi",
                "name": "npu-smi",
                "readOnly": True
            }],
            'ext_vol': [{
                "name": "ascend-driver",
                "hostPath": {"path": "/usr/local/Ascend/driver"}
            }, {
                "name": "ascend-add-ons",
                "hostPath": {"path": "/usr/local/Ascend/add-ons"}
            }, {
                "name": "npu-smi",
                "hostPath": {"path": "/usr/local/sbin/npu-smi"}
            }]
        }), inf_svc)


@spec_matcher(re.compile('^Hygon.*$'))
class HygonGPUStrategy(AbsGPUStrategy):

    def __init__(self, prod_info: ProductDTO, inf_svc: InfSvc):
        super().__init__(prod_info, GPUSpecItem(**{
            'resource': {'hygon.com/dcu': prod_info.gpu_count},
            'node_selector': {'feature.node.kubernetes.io/gpu-model': prod_info.gpu_model},
            'ext_mount': [{
                "mountPath": "/opt/hyhal",
                "name": "hyhal",
                "readOnly": True
            }],
            'ext_vol': [{
                "name": "hyhal",
                "hostPath": {"path": "/opt/hyhal"}
            }]
        }), inf_svc)


class IntelCPUStrategy(AbsGPUStrategy):

    def __init__(self, prod_info: ProductDTO, inf_svc: InfSvc):
        super().__init__(prod_info, GPUSpecItem(), inf_svc)


class ResourceGroupStrategy(AbsGPUStrategy):
    """
    专属资源组，有些逻辑特殊
    """

    def __init__(self, rg_info: ResourceGroup, prod_info: RGProductDTO|ProductDTO, inf_svc: InfSvc):

        # 获取 gpu 对应的 resource 和 selector
        resource = {}
        node_selector = {'aicp.group/resource_group': rg_info.rg_id}
        if prod_info.gpu_model and prod_info.gpu_count:
            gpu_spec_item = ResourceManager.create_gpu_strategy(prod_info, inf_svc, None).gpu_spec_item
            node_selector.update(gpu_spec_item.node_selector)
            resource.update(gpu_spec_item.vgpu_resource if prod_info.spec_type == 'vGPU' else gpu_spec_item.resource)

        if prod_info.rg_node_id:
            node_selector.update({'aicp.group/resource_group_node': prod_info.rg_node_id})

        super().__init__(prod_info, GPUSpecItem(**{
            'node_selector': node_selector,
            'tolerations': [{
                "key": "aicp.group/resource_group",
                "operator": "Equal",
                "value": rg_info.rg_id,
                "effect": "NoSchedule"
            }, {
                "key": "aicp.group/resource_group_node",
                "operator": "Exists",
                "effect": "NoSchedule"
            }],
            'res_req':  {'memory': '1Gi', 'cpu': 1} if prod_info.gpu_list else {},
            'ext_env': [{'name': 'NVIDIA_VISIBLE_DEVICES', 'value': ','.join(prod_info.gpu_list)}] if prod_info.gpu_list else [],
            'affinity': None,
            'resource': resource,
        }), inf_svc)


class ResourceManager:

    @staticmethod
    def create_gpu_strategy(prod_info: ProductDTO, inf_svc: InfSvc, rg_info: ResourceGroup=None) -> AbsGPUStrategy:
        if not prod_info:
            raise MaaSBaseException(Err.OBJECT_NOT_EXISTS, fields='规格信息')
        if rg_info:
            return ResourceGroupStrategy(rg_info, prod_info, inf_svc)
        if prod_info.aipods_type == 'only_cpu':
            return IntelCPUStrategy(prod_info, inf_svc)
        for pattern, _cls in GPU_SPECS.items():
            if pattern.match(prod_info.gpu_model):
                return _cls(prod_info, inf_svc)
        raise MaaSBaseException(Err.UNSUPPORT_OPERATION)
