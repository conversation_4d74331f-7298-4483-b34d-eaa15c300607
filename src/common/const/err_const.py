# -*- coding: utf-8 -*-
from enum import Enum


class Err(Enum):
    """
    异常常量
    """

    # 参数校验相关
    REQUIRE_PARAMS = (1100, '缺少参数[{fields}]', 'require params [{fields}]')
    VALIDATE_PARAMS = (1100, '参数校验失败[{message}]', 'validate param error [{message}]')
    ILLEGAL_STATUS_OPERATION = (1100, '资源[{resource_id}]处于[{status}]状态,不允许该操作',
                                'resource [{resource_id}] is in [{status}], illegal status operation')
    UNSUPPORT_OPERATION = (1100, '不支持该操作', "the operation is not supported")

    # 权限相关
    NOT_LOGIN = (1400, '用户未登录', 'user is not login')
    AUTH_INSUFFICIENT = (1400, '权限不足', 'Insufficient authority')
    AUTH_PLAT_ERR = (1400, '权限错误，接口需要平台[{plat}]权限', 'auth error, api need plat [{plat}] auth')
    AUTH_OWNER_ERR = (1400, '权限错误，非本人资源，无法操作', 'auth error, is not resource owner')
    AUTH_CONSUME_ERR = (1400, '权限错误，子账号没有消费权限', 'auth error, no auth for consume')

    # 资源已存在/不存在
    NOT_FOUND = (2100, '您查询的资源不存在', 'resource not found')
    OBJECT_NOT_EXISTS = (2100, '[{fields}]不存在', '[{fields}] not exists')
    OBJECT_EXISTS = (2110, '[{fields}]已存在', '[{fields}] already exists')

    # 资金相关
    ILLEGAL_LEASE_STATUS = (2200, '资源[{resource_id}]的租赁信息状态[{lease_status}], 请稍后再试',
                            'lease status [{lease_status}] of resource [{resource_id}], please try again later')
    NOT_ENOUGH_MONEY = (2400, '余额不足，需要支付[{need_cash}]元，请充值后再使用', 'not enough money, need pay ￥[{need_cash}]')

    # 配额相关
    QUOTA_EXCEED = (2500, '服务数[{svc_cnt}]达到上限，配额不足，请联系管理员', 'service count [{svc_cnt}] exceed quota')
    GPU_MODEL_QUOTA_EXCEED = (2501, 'GPU型号[{gpu_model}]配额不足，当前使用[{current_usage}]，总配额[{quota}]，请联系管理员', 'GPU model [{gpu_model}] quota exceed, current usage [{current_usage}], total quota [{quota}]')
    GPU_TOTAL_QUOTA_EXCEED = (2502, 'GPU总量配额不足，当前使用[{current_usage}]，总配额[{quota}]，请联系管理员', 'GPU total quota exceed, current usage [{current_usage}], total quota [{quota}]')
    SERVICE_QUOTA_EXCEED = (2503, '服务数量配额不足，当前使用[{current_usage}]，总配额[{quota}]，请联系管理员', 'Service quota exceed, current usage [{current_usage}], total quota [{quota}]')

    # 系统错误
    SERVER_ERR = (5000, '内部错误', 'system error')
    NOT_IMPLEMENT = (5000, '功能未实现', 'function is not implemented')
    INTERFACE_FAILED = (5000, '接口[{action}]请求失败: {message}', 'interface [{action}] request failed: {message}')


    def __new__(cls, code, msg_zh, msg_en):
        obj = object.__new__(cls)
        obj._value_ = str(code) + msg_zh
        obj.code = code
        obj.msg_zh = msg_zh
        obj.msg_en = msg_en
        return obj
