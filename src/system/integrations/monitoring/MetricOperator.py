# -*- coding: utf-8 -*-
from statistics import mean

from src.apps.inference_service.rsp_schema import InfSvc
from src.common.loggers import logger
from src.system.integrations.monitoring.client import PromethusClient


class MetricOperator:

    def __init__(self):
        self.prometheus_client = PromethusClient()

    def make_exper(self, metric: str, option, inf_svc: InfSvc):
        expr = get_metric_express(inf_svc.spec_info.get("aipods_type", ""))[metric]
        pod_selector = f"""pod=~'{"|".join(option.pod_filter)}', namespace='{option.namespace}'"""
        full_pod_selector = f"""pod=~'{"|".join(option.full_pod_filter)}', namespace='{option.namespace}'"""
        deployment_selector = f"""deployment=~'{"|".join(option.service_filter)}', namespace='{option.namespace}'"""
        istio_service_selector = f"""destination_app=~'{"|".join(option.service_filter)}',destination_service_namespace='{option.namespace}'"""
        gpu_selector = f"""exported_pod=~'{"|".join(option.pod_filter)}', exported_namespace='{option.namespace}'"""
        vgpu_selector = f"""podname=~'{"|".join(option.pod_filter)}', podnamespace='{option.namespace}'"""
        return expr.replace("$pod", pod_selector).replace("$deploy", deployment_selector)\
            .replace("$istio_service", istio_service_selector).replace("$time", str(option.step)+"s")\
            .replace("$full_pod", full_pod_selector).replace("$gpu_pod", gpu_selector).replace("$vgpu_pod", vgpu_selector)

    def query_metrics(self, options, inf_svc: InfSvc):
        is_query_range = False
        results = {}
        if options.start_time and options.end_time:
            is_query_range = True
        for metric in options.metrics:
            expr = self.make_exper(metric, options, inf_svc)

            if is_query_range:
                metric_ret = self.prometheus_client.query_range(expr, options.start_time, options.end_time, options.step)
            else:
                metric_ret = self.prometheus_client.query(expr)
            if metric_ret["status"] != "success":
                logger.error(f"get metric [{metric}] by option: {options} with expr: {expr} failed, "
                             f"ret: [{metric_ret}]")
                continue

            if is_query_range:
                for metric_r in metric_ret.get("data", {}).get("result", []):
                    metric_r["avg_value"] = round(mean([float(v[1]) for v in metric_r["values"]]), 3)
                    metric_r["max_value"] = round(max([float(v[1]) for v in metric_r["values"]]), 3)
                    metric_r["min_value"] = round(min([float(v[1]) for v in metric_r["values"]]), 3)
                    metric_r["current_value"] = metric_r["values"][-1][1]
            metric_ret["data"]["metric_name"] = metric
            results[metric] = metric_ret["data"]
        return results


metric_operator = MetricOperator()


def get_metric_express(aipods_type: str):
    metric_promql = {
        "pod_cpu_usage": """sum by (namespace, pod) (node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{$pod})""",
        "pod_memory_usage_wo_cache": """sum by (namespace, pod) (container_memory_working_set_bytes{job="kubelet", $pod})/1024/1024""",

        "pod_net_bytes_transmitted": """sum by (namespace) (rate(container_network_transmit_bytes_total{interface!~"^(cali.+|tunl.+|dummy.+|kube.+|flannel.+|cni.+|docker.+|veth.+|lo.*)", job="kubelet", $full_pod}[$time:1s])) / 1024""",
        "pod_net_bytes_received": """sum by (namespace) (rate(container_network_receive_bytes_total{interface!~"^(cali.+|tunl.+|dummy.+|kube.+|flannel.+|cni.+|docker.+|veth.+|lo.*)", job="kubelet", $full_pod}[$time:1s])) /1024""",

        "pod_memory_usage": """sum by (namespace, pod) (container_memory_usage_bytes{job="kubelet", image!="", $pod})""",

        "pod_gpu_util": """sum by (exported_namespace, exported_pod, gpu) (DCGM_FI_DEV_GPU_UTIL{$gpu_pod})""",
        "pod_gpu_mem_usage": """sum by (exported_namespace, exported_pod, gpu) (DCGM_FI_DEV_FB_USED{$gpu_pod})""",

        "qps": """sum by (destination_app, destination_service_namespace) (rate(istio_requests_total{$istio_service}[$time:1s]))""",
        "http_response_total": """sum by(destination_app, destination_service_namespace)(istio_requests_total{$istio_service})""",
    }
    if aipods_type == "vGPU":
        metric_promql.update({
            "pod_gpu_mem_usage":
            """
            sum by(exported_namespace, exported_pod, gpu) (
              label_replace(
                label_replace(
                  label_replace(
                    vGPU_device_memory_usage_in_bytes{$vgpu_pod},
                    "exported_pod", "$1", "podname", "(.*)"
                  ),
                  "exported_namespace", "$1", "podnamespace", "(.*)"
                ),
                "gpu", "$1", "vdeviceid", "(.*)"
              )
            ) / 1024 / 1024
            """,
        })
    return metric_promql