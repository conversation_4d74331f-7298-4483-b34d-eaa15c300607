# -*- coding: utf-8 -*-
from fastapi import APIRouter, Depends

from src.apps.depends import check_permission
from src.apps.tag.rsp_schema import ModelTag
from src.common.const.comm_const import UserPlat
from src.common.rsp_schema import ListResponse
from src.common.utils.data import wrap_rsp
from src.setting import settings

api_router = APIRouter(prefix="/api/tag", tags=["标签接口"], dependencies=[Depends(check_permission(UserPlat.ALL))])


@api_router.get("")
async def get() -> ListResponse[ModelTag]:
    return wrap_rsp([ModelTag(code=key, name=val) for key, val in settings.TAGS.items()])



