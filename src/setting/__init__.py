# -*- coding: utf-8 -*-
import os
import threading
import random
from time import sleep

import kr8s
import yaml


from .setting import Settings


# 注册配置文件变更事件
def watch_config():
    from ..common.loggers import task_logger as logger
    sleep(random.randint(1, 10))
    if os.getenv("ENV_CONF"):
        return
    while True:
        try:
            logger.info('监听配置文件')
            for action, cm in kr8s.watch("cm", label_selector="app=model-manage-server", namespace='maas-system'):
                if action == 'MODIFIED':
                    load_conf(cm)
        except Exception:
            logger.exception('监听配置资源异常')
        sleep(10)

def load_conf(cm=None):
    yml_config = {}
    data = {}

    if cm:
        yml_config.update(yaml.safe_load(cm.data.config_yaml))
    else:
        # 开发环境加载配置
        if ENV_CONF := os.getenv("ENV_CONF", None):
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

            for path in ['/manifests/base/config.yaml', f'/manifests/overlay/{ENV_CONF}/config.yaml']:
                with open(root_dir + path, 'r', encoding='utf-8') as f:
                    yml_config.update(yaml.safe_load(f) or {})

            # 如果设置了SKIP_K8S_CONFIG环境变量，则跳过kr8s.api的调用
            if not os.getenv("SKIP_K8S_CONFIG"):
                kr8s.api(kubeconfig=os.path.join(root_dir, "local/config.yaml"))
        else:
            # 集群环境加载配置
            config_path = '/code/config/config.yaml'
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    yml_config.update(yaml.safe_load(f))

    # 大小写转换
    for k, v in yml_config.items():
        fields = Settings.__fields__
        if k in fields:
            data[k] = v
        elif k.upper() in fields:
            data[k.upper()] = v
        elif k.lower() in fields:
            data[k.lower()] = v

    # 优先级 data > env(自动加载)
    global settings
    temp_settings = Settings(**data)
    if 'settings' not in globals():
        settings = temp_settings
    else:
        for k, v in temp_settings.dict().items():
            setattr(settings, k, v)
    print('加载配置: %s' % settings, flush=True)
    return settings

settings = load_conf()
threading.Thread(target=watch_config).start()