# -*- coding: utf-8 -*-
import threading
from time import sleep
from typing import Optional

import kr8s

from src.apps import job_curd
from src.apps.billing.curd import BillingCURD
from src.apps.inference_service.curd import inf_svc_curd
from src.apps.job.rsp_schema import Job
from src.common.const.comm_const import AlertReason
from src.common.const.comm_const import InfHealthStatus, JobStatus, JobAction, JOB_ACTION_MAPPING, JobReason, \
    PUSH_MODULE
from src.common.k8s_resource.base_operation import OperationManager
from src.common.loggers import task_logger as logger
from src.setting import settings
from src.system.integrations.push.push_client import push_client
from src.system.interface import PI


async def set_job_success(job):
    service_id = job.resource_id
    namespace = job.creator.lower()

    inf_svc = await inf_svc_curd.get_by_id(service_id)
    svc_name = OperationManager.get_operation(inf_svc).svc_name
    await job_curd.base_update(job.job_id, {'status': JobStatus.SUCCESS.value})

    # 更新service状态
    params = {'transition_status': '', 'health_status': InfHealthStatus.HEALTH.value}
    if job.action == JobAction.DeployInference.value:
        external_url = settings.INFERENCE_SERVICE_HOST.replace("{namespace}", namespace).replace("{service}", service_id)
        svc = kr8s.get('svc', svc_name, namespace=namespace)
        intranet_url = f"{svc[0].spec.clusterIP}:80"  # noqa
        params.update({'intranet_url': intranet_url, 'external_url': external_url})
    await inf_svc_curd.base_update(job.resource_id, params)

    # 更新计费
    if not inf_svc.resource_group_id:
        await update_service_lease(job, inf_svc.creator, sku_id=inf_svc.spec_info['sku_id'], instance_num=job.target_replica, node_num=inf_svc.node_num)

    # socket 推送
    threading.Thread(target=push_client.push_socket,
                     args=(job.action, service_id, JobStatus.SUCCESS.value, [inf_svc.creator, job.creator]),
                     kwargs={"op_id": job.job_id, "resource_type": PUSH_MODULE}).start()


async def set_job_failed(job, real_replica, reason='', msg=''):

    # 更新状态
    await job_curd.base_update(job.job_id, {'status': JobStatus.FAILED.value, 'reason': reason, 'remark': msg})
    await inf_svc_curd.base_update(job.resource_id, {'transition_status': '', 'health_status': InfHealthStatus.UNHEALTH.value})

    inf_svc = await inf_svc_curd.get_by_id(job.resource_id)
    # 不使用资源组并且不是创建任务时，根据实际的副本数更新计费信息（创建任务失败直接删除，不需要计费）
    if not inf_svc.resource_group_id and job.action != JobAction.DeployInference.value:
        await update_service_lease(job, inf_svc.creator, sku_id=inf_svc.spec_info['sku_id'], instance_num=real_replica, node_num=1)

    # 推送事件
    push_client.send_maas_alert_event(AlertReason.MaaSExecuteJobFailed, message=f"推理服务 {job.resource_id} 操作 {job.action} 执行失败")
    user = PI.user_interface.get_user_by_id(inf_svc.creator)
    push_client.push_msg_to_user("aicp_maas_error", inf_svc.creator, {
        "username": user.user_name,
        "zone": settings.QINGCLOUD_ZONE,
        "id": job.resource_id,
        "action": JOB_ACTION_MAPPING.get(job.action, "处于风险中")
    })
    threading.Thread(target=push_client.push_socket,
                     args=(job.action, job.resource_id, JobStatus.FAILED.value, [inf_svc.creator, job.creator]),
                     kwargs={"op_id": job.job_id, "resource_type": PUSH_MODULE}).start()


async def update_service_lease(job: Job, user_id, sku_id: Optional[str] = None, instance_num: Optional[int] = None, node_num: Optional[int] = 1):
    # 计费触发的动作，不需要调用计费接口
    if job.reason in [JobReason.ARREARS_SUSPEND.value, JobReason.ARREARS_CEASE.value, JobReason.ARREARS_RESUME.value]:
        return

    try_cnt = 6
    service_id = job.resource_id
    while try_cnt > 0:
        try:
            if job.action == JobAction.DeleteInference.value:
                logger.info(f'服务[{user_id}/{service_id}] 销毁租赁')
                await BillingCURD.unlease(resource_id=service_id, user_id=user_id)
            else:
                logger.info(f'服务[{user_id}/{service_id}] 更新租赁，规格[{sku_id}] 副本数[{instance_num * node_num}]')
                await BillingCURD.lease(resource_id=service_id, user_id=user_id, replica=instance_num * node_num, sku_id=sku_id)
            break
        except Exception:  # noqa
            try_cnt -= 1
            if try_cnt == 0:
                logger.exception(f'服务[{user_id}/{service_id}]更新计费失败')
                push_client.send_maas_alert_event(AlertReason.MaaSInvokeBillingFailed,
                                      f"{service_id} 调用billing接口 {job.action} 更新计费失败")
            else:
                sleep(10)
