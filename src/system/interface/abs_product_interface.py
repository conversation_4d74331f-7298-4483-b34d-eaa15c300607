# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod

from src.common.const.err_const import Err
from src.common.exceptions import MaaSBaseException


class AbsProductInterface(ABC):
    """
    产品相关接口
    """

    @abstractmethod
    def get_product_by_id(self, product_id: str):
        """
        根据产品 id 查询查询信息
        :param product_id: 产品id
        :return: 产品信息
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)

    @abstractmethod
    def get_resource_id(self, product_id) -> str:
        """
        获取资源id，对于资源id 和 产品id 不相同情况，需要转换
        :param product_id: 产品id
        :return: 资源id
        """
        raise MaaSBaseException(Err.NOT_IMPLEMENT)
