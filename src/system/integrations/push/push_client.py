# -*- coding: utf-8 -*-
import json
import os
from datetime import datetime

import requests
from kr8s.objects import Event

from src.common.const.comm_const import Al<PERSON><PERSON><PERSON>on, JOB_ACTION_MAPPING, JOB_STATUS_MAPPING
from src.common.dto import AccessKey
from src.common.loggers import logger
from src.common.utils.data import get_signature
from src.common.utils.id_tool import uuid
from src.setting import settings
from src.system.interface import PI
from src.system.interface.qingcloud.iaas_client import iaas_client


class PushClient(object):

    @staticmethod
    def push_socket(action: str, resource: str, status: str, user_ids: list[str], resource_type: str = None,
                    reason: str = None, op_id: str = None):
        """ 推送 websocket 事件到 console """
        if os.getenv("ENV_CONF") == 'dev':
            return
        for user_id in set(user_ids):
            ak: AccessKey = PI.user_interface.get_access_key(user_id)
            data = {
                "action": JOB_ACTION_MAPPING.get(action, action),
                "op_id": op_id,
                "resource": resource,
                "child_resource": resource,
                "status": JOB_STATUS_MAPPING.get(status, status),
                "user_id": user_id,
                "reason": reason,
                "resource_type": resource_type
            }
            req_url = get_signature(method="POST", url='/push/ms/', ak=ak.access_key_id, sk=ak.secret_access_key,
                                    params={"user_id": user_id})
            url = f"{settings.AI_CLOUD_PUSH_SERVER}?{req_url}"
            ret = requests.post( url, json=data)
            logger.info(f"websocket push [{url}][{data}]: [{ret.text}]")

    @staticmethod
    def send_maas_alert_event(reason: AlertReason, message):
        """ 推送告警事件到管理端 """
        current_time = datetime.now().astimezone().isoformat()
        body = {
                "apiVersion": 'v1',
                "kind": 'Event',
                "metadata": {
                    "name": "model-manage-server" + uuid(None),
                    "namespace": "maas-system",
                },
                "involvedObject": {
                    "apiVersion": 'apps/v1',
                    "kind": 'Deployment',
                    "name": "model-manage-server",
                    "namespace": "maas-system",
                },
                "reportingComponent": "",
                "reportingInstance": "",
                "reason": reason.value,
                "message": message,
                "type": 'Warning',
                "firstTimestamp": current_time,
                "lastTimestamp": current_time
        }
        event = Event(namespace="maas-system", resource=body)  # noqa
        event.create()

    @staticmethod
    def push_msg_to_user(template_code, user_id, data: dict):
        """ 推送事件给用户 """
        action = "SendMsgHubPost"
        req = {
            "users": [user_id],
            "template_code": template_code,
            "receiver_filter_type": "specified",
            "render_data": json.dumps(data),
            "notify_types": ["sms", "email", "web", "webhook"]
        }
        try:
            rsp = iaas_client.send_request(action, req)
            if rsp.get('ret_code') != 0:
                logger.error("推送用户事件异常, action=[%s] rsp[%s]" % (action, rsp))
        except Exception as e:
            logger.exception(f"推送用户事件异常, action=[{action}]")
            rsp = {"ret_code": -1, "ret_msg": str(e)}
        return rsp

push_client = PushClient()